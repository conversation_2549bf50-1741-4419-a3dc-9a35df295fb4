{"name": "front-admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@phosphor-icons/react": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@xyflow/react": "^12.6.0", "dayjs": "^1.11.13", "html2canvas-pro": "^1.5.10", "jspdf": "^3.0.1", "next": "^15.3.4", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-pdf": "^9.2.1", "sonner": "^2.0.3", "use-mask-input": "^3.4.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ourtrip/ui": "^1.0.19", "@tailwindcss/postcss": "^4.0.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "date-fns": "^4.1.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "15.2.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.3.0", "jose": "^6.0.10", "prettier": "^3.5.3", "tailwindcss": "^4.0.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "zod": "^3.24.2"}}