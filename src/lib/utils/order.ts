import { Order } from '@/lib/definitions/order';
import { capitalizeFirstLetter } from './strings';

export const mountOrderResumeString = (order: Order) => {
  const count = order.itens.reduce((acc: any, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1;
    return acc;
  }, {});
  return Object.entries(count)
    .map(([type, quantity]) => `${quantity}x ${capitalizeFirstLetter(type)}`)
    .join(', ');
};
