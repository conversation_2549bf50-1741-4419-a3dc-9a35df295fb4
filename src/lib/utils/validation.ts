export const isValidCpf = (formatedCpf: string): boolean => {
  const cpf = formatedCpf.replace(/\D/g, '');

  if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) {
    return false;
  }

  // Calcular el primer dígito verificador
  let soma = 0;

  for (let i = 0; i < 9; i++) {
    soma += parseInt(cpf.charAt(i), 10) * (10 - i);
  }
  let digito1 = 11 - (soma % 11);
  if (digito1 > 9) {
    digito1 = 0;
  }

  // Calcular el segundo dígito verificador
  soma = 0;
  for (let i = 0; i < 10; i++) {
    soma += parseInt(cpf.charAt(i), 10) * (11 - i);
  }
  let digito2 = 11 - (soma % 11);
  if (digito2 > 9) {
    digito2 = 0;
  }

  // Verificar si los dígitos verificadores son iguales a los últimos dos dígitos del CPF
  if (
    parseInt(cpf.charAt(9), 10) === digito1 &&
    parseInt(cpf.charAt(10), 10) === digito2
  ) {
    return true;
  }
  return false;
};

export const isValidCNPJ = (formatedCnpj: string): boolean => {
  const cnpj = formatedCnpj.replace(/[^\d]+/g, '');

  if (cnpj.length !== 14) {
    return false;
  }

  if (/^(\d)\1+$/.test(cnpj)) {
    return false;
  }

  let length = cnpj.length - 2;
  let numbers = cnpj.substring(0, length);
  const digitosVerificadores = cnpj.substring(length);

  let soma = 0;
  let pos = length - 7;

  for (let i = length; i >= 1; i--) {
    soma += parseInt(numbers.charAt(length - i), 10) * pos--;
    if (pos < 2) {
      pos = 9;
    }
  }

  const resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);

  if (resultado !== parseInt(digitosVerificadores.charAt(0), 10)) {
    return false;
  }

  length += 1;
  numbers = cnpj.substring(0, length);
  soma = 0;
  pos = length - 7;

  for (let i = length; i >= 1; i--) {
    soma += parseInt(numbers.charAt(length - i), 10) * pos--;
    if (pos < 2) {
      pos = 9;
    }
  }

  const segundoDigito = soma % 11 < 2 ? 0 : 11 - (soma % 11);

  return segundoDigito === parseInt(digitosVerificadores.charAt(1), 10);
};

export const isFullName = (name: string) =>
  /^[A-Za-zÀ-ÿ]+(?: [A-Za-zÀ-ÿ]+)+$/.test(name);

export const isValidName = (name: string) =>
  /^[A-Za-zÀ-ÿ\s.'-]+(?: [A-Za-zÀ-ÿ\s.'-]+)*$/.test(name);

export const isValidEmail = (email: string) =>
  /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(email);
