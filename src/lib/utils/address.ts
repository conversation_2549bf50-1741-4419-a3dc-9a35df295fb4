type Address = {
  city: string;
  complement: string;
  country: string;
  neighborhood: string;
  uf: string;
  street: string;
  postalCode: string;
  number: string;
};

export const mountAddressValues = (data: any): Address => {
  return {
    city: data?.localidade,
    complement: data?.complemento,
    country: 'Brasil',
    neighborhood: data.bairro,
    uf: data.uf,
    street: data.logradouro,
    postalCode: data.cep,
    number: ''
  };
};
