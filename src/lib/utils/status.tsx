import { ReactNode } from 'react';
import {
  ClockCountdown,
  XCircle,
  Check,
  Prohibit
} from '@phosphor-icons/react';

type BadgeType =
  | 'success'
  | 'warning'
  | 'danger'
  | 'info'
  | 'white'
  | 'primary'
  | 'secondary';

export type StatusType =
  | 'CONFIRMED'
  | 'PENDING_PAYMENT'
  | 'PAYMENT_EXPIRED'
  | 'CANCELED';

export const getStatusType = (status: StatusType) => {
  switch (status) {
    case 'PENDING_PAYMENT':
      return 'warning';
    case 'PAYMENT_EXPIRED':
      return 'danger';
    case 'CONFIRMED':
      return 'success';
    case 'CANCELED':
      return 'danger';
    default:
      return 'info';
  }
};
export const getStatusDisplay = (status: StatusType) => {
  switch (status) {
    case 'PENDING_PAYMENT':
      return 'Pagamento pendente';
    case 'PAYMENT_EXPIRED':
      return 'Pagamento expirado';
    case 'CONFIRMED':
      return 'Pedido confirmado';
    case 'CANCELED':
      return 'Cancelado';
    default:
      return 'Pagamento';
  }
};
export const getStatusIcon = (status: StatusType) => {
  switch (status) {
    case 'PENDING_PAYMENT':
      return <ClockCountdown size={18} weight='bold' />;
    case 'PAYMENT_EXPIRED':
      return <XCircle size={18} weight='bold' />;
    case 'CONFIRMED':
      return <Check size={18} weight='bold' />;
    case 'CANCELED':
      return <Prohibit size={18} weight='bold' />;
    default:
      return null;
  }
};

export const orderStatus = {
  CANCELED: {
    text: 'Cancelado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PENDING_CANCEL: {
    text: 'Cancelamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  CONFIRMED: {
    text: 'Pedido confirmado',
    icon: <Check size={14} weight='bold' />,
    type: 'success'
  },
  PENDING_PAYMENT: {
    text: 'Pagamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  PENDING_ANTIFRAUD: {
    text: 'Pagamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  PAYMENT_ERROR: {
    text: 'Pagamento não aprovado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PAYMENT_REJECTED: {
    text: 'Pagamento não aprovado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PAYMENT_EXPIRED: {
    text: 'Pagamento expirado',
    icon: <XCircle size={14} weight='bold' />,
    type: 'danger'
  },
  PENDING_CONFIRMATION: {
    text: 'Processando reserva',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'info'
  },
  BOOKING_ERROR: {
    text: 'Oferta indisponível',
    icon: <XCircle size={14} weight='bold' />,
    type: 'danger'
  }
};

export const orderPaymentStatus: Record<
  string,
  { text: string; icon: ReactNode; type: BadgeType }
> = {
  CANCELED: {
    text: 'Cancelado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PENDING_CANCEL: {
    text: 'Cancelamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  CONFIRMED: {
    text: 'Pago',
    icon: <Check size={14} weight='bold' />,
    type: 'success'
  },
  PENDING_PAYMENT: {
    text: 'Pagamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  PENDING_ANTIFRAUD: {
    text: 'Pagamento pendente',
    icon: <ClockCountdown size={14} weight='bold' />,
    type: 'warning'
  },
  PAYMENT_ERROR: {
    text: 'Pagamento não aprovado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PAYMENT_REJECTED: {
    text: 'Pagamento não aprovado',
    icon: <Prohibit size={14} weight='bold' />,
    type: 'danger'
  },
  PAYMENT_EXPIRED: {
    text: 'Pagamento expirado',
    icon: <XCircle size={14} weight='bold' />,
    type: 'danger'
  },
  PENDING_CONFIRMATION: {
    text: 'Pago',
    icon: <Check size={14} weight='bold' />,
    type: 'success'
  },
  BOOKING_ERROR: {
    text: 'Oferta indisponível',
    icon: <XCircle size={14} weight='bold' />,
    type: 'danger'
  }
};

export const orderPaymentApi = {
  MERCADO_PAGO: {
    name: 'Mercado Pago',
    image: 'https://ourtrips3.s3.amazonaws.com/images/gateway_mercado_pago.png'
  },
  BARTE_API: {
    name: 'Barte',
    image: 'https://ourtrips3.s3.amazonaws.com/images/gateway_barte.png'
  },
  ZRU_API: {
    name: 'ZRU',
    image: 'https://ourtrips3.s3.amazonaws.com/images/gateway_zru.png'
  }
};
