import {
  SearchDTO,
  SearchPayload,
  SearchPayloadRooms,
  SearchUrlParams,
  searchPayloadDefaultValue
} from '@/lib/definitions/search';

export const mountRoomDistributionStringFromObject = (
  distribution: SearchPayloadRooms[]
) =>
  distribution.reduce((acc, room, index) => {
    return `${acc}${room.adults}${
      room.kids.length > 0 ? `-${room.kids.join('-')}` : ''
    }${index < distribution.length - 1 ? '!' : ''}`;
  }, '');

export const mountSearchUrlByPayload = (searchPayload: SearchPayload) => {
  const roomsString = mountRoomDistributionStringFromObject(
    searchPayload.distribution
  );

  const { destination } = searchPayload;
  return `destination=${destination.display}&code=${destination.id}&group=${destination.group}&latlng=${destination?.location?.latitude}!${destination?.location?.longitude}&checkin=${searchPayload.checkin}&checkout=${searchPayload.checkout}&distribution=${roomsString}`;
};

export const mountHotelUrlByPayload = (payload: SearchDTO) => {
  const roomsString = mountRoomDistributionStringFromObject(
    payload.distribution
  );

  return `distribution=${roomsString}&checkin=${payload.checkin}&checkout=${payload.checkout}`;
};

export const mountRoomDistributionObjectFromString = (distribution: string) => {
  return distribution.split('!').map<SearchPayloadRooms>(room => {
    const roomDistribution = room.split('-');
    return {
      adults: Number(roomDistribution.shift()),
      kids: roomDistribution.map(age => Number(age))
    };
  });
};

const plural = (value: number) => (value !== 1 ? 's' : '');

export const mountRoomDistributionStringFromDistributionAndRoom = (
  distribution: SearchPayloadRooms[],
  room: { adults: number; kids: number }
) => {
  return `${distribution.length} quarto${plural(distribution.length)}, ${
    room.adults
  } adulto${plural(room.adults)} ${
    room.kids > 0 ? `, ${room.kids} criança${plural(room.kids)}` : ''
  }`;
};

export const mountRoomDistributionStringFromDistribution = (
  distribution: SearchPayloadRooms
) => {
  return `${distribution.adults} adulto${plural(distribution.adults)} ${
    distribution.kids.length > 0
      ? `, ${distribution.kids.length} criança${plural(
          distribution.kids.length
        )}`
      : ''
  }`;
};

export const mountPayloadBySearchUrl = ({
  destination,
  code,
  group,
  latlng,
  checkin,
  checkout,
  distribution
}: SearchUrlParams): SearchPayload => {
  const [latitude, longitude] = latlng.split('!');

  return {
    ...searchPayloadDefaultValue,
    destination: {
      display: destination,
      group,
      id: code,
      location: {
        latitude: Number(latitude),
        longitude: Number(longitude)
      }
    },
    distribution: mountRoomDistributionObjectFromString(distribution),
    checkin,
    checkout
  };
};
