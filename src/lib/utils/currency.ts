/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
export enum PaymentMethodIdEnum {
  mastercard = 'Mastercard',
  visa = 'Visa',
  amex = 'American Express',
  pix = 'Pix',
  elo = 'Elo'
}

export const convertNumberToCurrency = (
  currency: string,
  value: number,
  locale?: string
) => {
  locale = locale ?? 'pt-BR';

  const formatter = new Intl.NumberFormat(locale?.replace('_', '-'), {
    style: 'currency',
    currencyDisplay: 'code',
    currency
  });

  return formatter
    .format(value / 100)
    .replace(currency, '')
    .trim();
};
