/* eslint-disable import/no-duplicates */
import { format, Locale, parse } from 'date-fns';
import { enUS, es, pt, ptBR } from 'date-fns/locale';
import { capitalizeFirstLetter } from './strings';
import { TZDate } from '@date-fns/tz';

export const convertStringToUTCDate = (data: string) => {
  const date = new Date(data);
  return data.includes('T')
    ? new Date(data)
    : new Date(date.valueOf() + date.getTimezoneOffset() * 60 * 1000);
};

export const convertStringToDefaultFormatedDate = (date: string) => {
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    const parsedDate = parse(date, 'yyyy-MM-dd', new Date());
    return format(parsedDate, 'dd/MM/yyyy', {
      locale: ptBR
    });
  }

  const convertedDate = convertStringToUTCDate(date);
  const tzDate = new TZDate(convertedDate.getTime(), 'America/Sao_Paulo');

  return format(tzDate, 'dd/MM/yyyy', {
    locale: ptBR
  });
};

export const convertStringToDateWithMask = (date: string, mask: string) => {
  const convertedDate = convertStringToUTCDate(date);
  const tzDate = new TZDate(convertedDate.getTime(), 'America/Sao_Paulo');
  return format(tzDate, mask, {
    locale: ptBR
  });
};

export const convertStringToFormatedDateByLocale = (
  value: string,
  locale: Locale
) => {
  if (!value || value === '') return '';

  const date = convertStringToUTCDate(value);
  const tzDate = new TZDate(date.getTime(), 'America/Sao_Paulo');
  const day = capitalizeFirstLetter(format(tzDate, 'PPPP', { locale }));

  return `${day}`;
};

export const convertStringToFormatedDate = (value: string) => {
  const date = convertStringToUTCDate(value);
  const tzDate = new TZDate(date.getTime(), 'America/Sao_Paulo');

  const dayOfWeek = capitalizeFirstLetter(
    format(tzDate, 'EEEEEE', { locale: ptBR })
  );
  const dayOfMonth = format(tzDate, 'dd', { locale: ptBR });
  const month = capitalizeFirstLetter(format(tzDate, 'MMMM', { locale: ptBR }));
  const year = format(tzDate, 'yyyy', { locale: ptBR });

  return `${dayOfWeek}, ${dayOfMonth} de ${month}, ${year}`;
};

export const convertStringToFormatedDateWithDate = (value: string) => {
  const date = convertStringToUTCDate(value);
  const tzDate = new TZDate(date.getTime(), 'America/Sao_Paulo');

  const dayOfWeek = capitalizeFirstLetter(
    format(tzDate, 'EEEEEE', { locale: ptBR })
  );
  const dayOfMonth = format(tzDate, 'dd', { locale: ptBR });
  const month = capitalizeFirstLetter(format(tzDate, 'MMMM', { locale: ptBR }));
  const year = format(tzDate, 'yyyy', { locale: ptBR });
  const time = format(tzDate, 'HH:mm:ss', { locale: ptBR });

  return `${dayOfWeek}, ${dayOfMonth} de ${month}, ${year} às ${time}`;
};

export const getDateFNSLocale = (locale: string) => {
  switch (locale.toUpperCase()) {
    case 'PT_BR':
      return ptBR;
    case 'PT_PT':
      return pt;
    case 'EN_US':
      return enUS;
    case 'ES_ES':
      return es;
    default:
      return ptBR;
  }
};

export const timeSince = (date: number) => {
  const seconds = Math.floor((new Date().getTime() - date) / 1000);

  let interval = seconds / 31536000;

  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'anos' : 'ano'
    } atrás`;
  }
  interval = seconds / 2592000;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'meses' : 'mês'
    } atrás`;
  }
  interval = seconds / 86400;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'dias' : 'dia'
    } atrás`;
  }
  interval = seconds / 3600;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'horas' : 'hora'
    } atrás`;
  }
  interval = seconds / 60;
  if (interval > 1) {
    return `há ${Math.floor(interval)} ${
      Math.floor(interval) > 1 ? 'minutes' : 'minuto'
    } atrás`;
  }
  return `há ${Math.floor(seconds)} ${
    Math.floor(interval) > 1 ? 'segundos' : 'segundo'
  } atrás`;
};

export const convertSecondsToMinutes = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const formatedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formatedSeconds =
    remainingSeconds < 10 ? `0${remainingSeconds}` : remainingSeconds;

  return `${formatedMinutes}:${formatedSeconds}`;
};
