export type TripcashPayload = {
  id?: string;
  value: number;
  percent: number;
  percentOnTime: number;
  title: string;
  type: 'ALL' | 'HOTELS' | 'DESTINATIONS';
  description: string;
  destinations: string[];
  hotels: string[];
  expiresAt: string;
  active: boolean;
};

export type TripcashResponse = {
  id: string;
  value: number;
  percent: number;
  percentOnTime: number;
  title: string;
  type: 'ALL' | 'HOTELS' | 'DESTINATIONS';
  description: string;
  destinations: string[];
  hotels: string[];
  expiresAt: string;
  active: boolean;
};

export const tripcashInitialValues: TripcashPayload = {
  value: 0,
  percent: 0,
  percentOnTime: 0,
  title: '',
  type: 'ALL',
  destinations: [],
  hotels: [],
  description: '',
  expiresAt: '',
  active: false
};
