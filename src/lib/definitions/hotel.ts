type HotelAmenity = {
  code?: string;
  description?: string;
  icon?: string;
};

export type HotelAmenitiesGroup = {
  code?: string;
  description?: string;
  icon?: string;
  amenities?: HotelAmenity[];
};

export type HotelPhoto = {
  url?: string;
  smallUrl?: string;
};

type HotelAddressLocation = {
  latitude?: string;
  longitude?: string;
};

type HotelAddress = {
  fullAddress?: string;
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  location?: HotelAddressLocation;
};

export type HotelDetails = {
  id?: string;
  name?: string;
  description?: string;
  type?: string;
  stars?: string;
  phone?: string;
  email?: string;
  address?: HotelAddress;
  rating?: string;
  photoCover?: HotelPhoto;
  photos?: HotelPhoto[];
  amenitiesGroups?: HotelAmenitiesGroup[];
};

export type HotelOfferPrices = {
  price: number;
  taxValue: number;
  finalPrice: number;
  paymentType: string;
  pricingType: string;
  currency: string;
  currencySymbol: string;
};

type HotelOfferCancellationPolicies = {
  refundable: boolean;
  refundObservations: string;
  cancellationLimitDate: string;
};

type HotelOfferMealPlan = {
  code: string;
  description: string;
};

export type HotelOffer = {
  accommodationIndex: number;
  token: string;
  prices: HotelOfferPrices;
  adults: number;
  kids: number[];
  rateComments: any[];
};

export type HotelOfferPlan = {
  token: string;
  accommodationId: string;
  accommodationName: string;
  ratePlan: string;
  cancellationPolicies: HotelOfferCancellationPolicies;
  mealPlan: HotelOfferMealPlan;
  photoCover: HotelPhoto;
  photos: HotelPhoto[];
  amenitiesGroups: HotelAmenitiesGroup[];
  offers: HotelOffer[];
};

export type HotelOffersCashbackInfo = {
  formatedPercent: string;
  formatedValue: string;
  percent: number;
  value: number;
};

export type CancellationPolicies = {
  id: string;
  refundable: boolean;
  refundableOriginalInfo: boolean;
  refundObservations: string;
  cancellationLimitDate: string;
};

export type Guest = {
  id: string;
  name: string;
  surname: string;
  document: string;
  age: number;
  child: boolean;
};

export type Room = {
  id: string;
  accommodationId: string;
  accommodationIndex: number;
  accommodationName: string;
  cancellationPolicies: CancellationPolicies;
  mealPlan: string;
  mealPlanDisplay: string;
  guests: Guest[];
};
