export type SearchPayloadRooms = {
  adults: number;
  kids: number[];
};

type DestinationGroup = 'CITY' | 'HOTEL' | '';
export interface LatLng {
  latitude: number;
  longitude: number;
}

export type SearchFilters = {
  mealPlans: string[];
  stars: string[];
  acommodationTypes: string[];
  amenities: string[];
  price: string | null;
  hotelName: string | null;
  cancellationPolicyRefundable: boolean;
  tripcash: string[];
};

export type Destination = {
  display: string;
  id: string;
  group: DestinationGroup;
  location: LatLng;
};

export type ISearchBoxPayload = {
  destination: Destination;
  checkin: Date;
  checkout: Date;
};

export type SearchPayload = {
  destination: Destination;
  checkin: string;
  checkout: string;
  distribution: SearchPayloadRooms[];
  page: number;
  currency?: string;
  limit: number;
  searchId: string;
  sorting: string;
  filters: SearchFilters;
};

export type SearchDTO = {
  checkin: string;
  checkout: string;
  distribution: SearchPayloadRooms[];
};

export type DestinationSearchGroup = {
  group: DestinationGroup;
  display: string;
  itens: Destination[];
};

export type IDestinationSearchResponse = {
  suggestion: {
    groups: DestinationSearchGroup[];
  };
};

export type SearchUrlParams = {
  destination: string;
  code: string;
  group: DestinationGroup;
  latlng: string;
  checkin: string;
  checkout: string;
  distribution: string;
};

type SearchResponsePaging = {
  page: number;
  limit: number;
  total: number;
};

export type SearchResponseFiltersApplied = {
  id: string;
  type: string;
  display: string;
  applied: boolean;
  value: string;
};

type SearchResponseMainFilterOptions = {
  id: string;
  applied: boolean;
  display: string;
  quantity: number;
};

type SearchResponsePrices = {
  text: string;
  value: string;
};

export type SearchResponseMainFilters = {
  id: keyof SearchFilters;
  type: string;
  display: string;
  applied: boolean;
  min?: number;
  max?: number;
  appliedMin?: number;
  appliedMax?: number;
  formatedMin?: string;
  formatedMax?: string;
  options?: SearchResponseMainFilterOptions[];
};

export type SearchSorting = {
  selected: boolean;
  display: string;
  label: string;
};

export type SearchResponseMealPlan = {
  code: string;
  description: string;
};

export type SearchResultAcommodationPhoto = {
  url: string;
  smallUrl: string;
};

export type SearchResponseResult = {
  accomodation: {
    id: string;
    fullAddress: string;
    destinations: string[];
    type: string;
    address: { fullAddress: string };
    name: string;
    stars: number;
    photosCover: SearchResultAcommodationPhoto;
    amenitiesGroups: null;
  };
  detailsUrl: string;
  mealPlan: SearchResponseMealPlan;
  refundable: boolean;
  position: number;
  rate: number | null;
  lastRooms: number;
  distance: number;
  unavailable: boolean;
  price: {
    currency: string;
    currencySymbol: string;
    main: string;
    mainText: string;
    price: number;
    taxMessage: string;
    cashbackInfo: {
      formatedPercent: string;
      formatedValue: string;
      percent: number;
      value: number;
    };
    summary: {
      taxesAndFees: SearchResponsePrices;
      total: SearchResponsePrices;
      totalWithoutTaxes: SearchResponsePrices;
      unitPrice: SearchResponsePrices;
    };
  };
};

export interface ISearchResponse {
  searchId: string;
  checkin: string;
  checkout: string;
  paging: SearchResponsePaging;
  filtersApplied: SearchResponseFiltersApplied[];
  filtersMain: SearchResponseMainFilters[];
  sorting: SearchSorting[];
  result: SearchResponseResult[];
}

export const roomInitialValues = {
  adults: 2,
  kids: []
};

export const filtersInitialValues = {
  mealPlans: [],
  stars: [],
  acommodationTypes: [],
  amenities: [],
  price: null,
  hotelName: null,
  cancellationPolicyRefundable: false,
  tripcash: []
};

export const searchPayloadDefaultValue: SearchPayload = {
  destination: {
    display: '',
    id: '',
    group: '',
    location: { latitude: 0, longitude: 0 }
  },
  checkin: '',
  checkout: '',
  distribution: [{ ...roomInitialValues }],
  page: 0,
  limit: 20,
  searchId: '',
  sorting: 'relevance',
  filters: filtersInitialValues
};
