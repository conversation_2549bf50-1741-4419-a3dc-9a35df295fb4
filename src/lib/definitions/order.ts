import { BookingPrice, BookingRoom, BookingStatusEnum } from './booking';
import { HotelPhoto } from './hotel';
import { Customer } from './customer';

export type StatusType =
  | 'CONFIRMED'
  | 'PENDING_PAYMENT'
  | 'PAYMENT_EXPIRED'
  | 'CANCELED';

type OrderPriceDetail = {
  value: number;
  formattedValue: string;
  description: string;
};

export type TripcashInfo = {
  formatedPercent: string;
  formatedValue: string;
  percent: number;
  value: number;
};

export type Price = {
  price: OrderPriceDetail;
  priceWithTax: OrderPriceDetail;
  finalPrice: OrderPriceDetail;
  discount: OrderPriceDetail;
  priceWithDiscount: OrderPriceDetail;
  installmentAmount: OrderPriceDetail;
  installments: number;
  currency: string;
  taxValue: OrderPriceDetail;
  currencySymbol: string;
  tripcashInfo: { planId: string } & TripcashInfo;
};

export type PayerIdentification = {
  documentType: string;
  documentNumber: string;
};

export type Phone = {
  ddi: string;
  areaCode: string;
  number: string;
};

export interface Address {
  street: string;
  number: string;
  complement: string;
  neighborhood: string;
  postalCode: string;
  city: string;
  uf: string;
  country: any;
}

export type PaymentResponse = {
  paymentResponseId: string;
  status: string;
  detail: string;
  qrCodeBase64: string;
  qrCode: string;
  qrCodeUrl: any;
  paymentMethodId: string;
  paymentApi: string;
  creationDate: string;
  captureDate: any;
  secondsRemaining: number;
};

export type Payer = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  payerIdentification: PayerIdentification;
  phone: Phone;
  address: Address;
};

export type Payment = {
  id: string;
  method: 'PIX' | 'CREDIT_CARD';
  paymentApi: any;
  token: any;
  issuerId: any;
  buyerUuid: any;
  paymentMethodId: any;
  transactionAmount: number;
  transactionAmountRefunded: any;
  transactionAmountOriginal: number;
  feeAmount: number;
  installmentAmount: number;
  installments: number;
  installmentsRate: any;
  payer: Payer;
  paymentResponse: PaymentResponse;
  antifraudResponse: any;
  authorizationCode: any;
  description: string;
};

type OrderItem = {
  id: string;
  index: number;
  type: 'HOTEL';
  finalPrice: number;
  booking: {
    id: string;
    additionalInformation?: string;
    channel: string;
    status: BookingStatusEnum;
    orderStatus: string;
    statusDisplay: string;
    checkin: string;
    checkout: string;
    reference: string;
    reservationCode: string;
    hotel: {
      id: string;
      name: string;
      stars: string;
      address: string;
      photoCover: HotelPhoto;
      phone: string;
      type: string;
    };
    providerAccount: string;
    price: BookingPrice;
    orderItemCode: any;
    rooms: BookingRoom[];
  };
  cashbackTransaction: any;
};

type OrderPrice = {
  adjustments: { description: string; formattedValue: string; value: number }[];
  price: OrderPriceDetail;
  finalPrice: OrderPriceDetail;
  discountAmount: any;
  priceWithDiscount: any;
  installments: number;
  installmentsFormatted: OrderPriceDetail;
  installmentAmount: OrderPriceDetail;
  originalPrice: OrderPriceDetail;
  taxValue: OrderPriceDetail;
  currency: string;
  currencySymbol: string;
  cashbackInfo: any;
};

type OrderPaymentResponse = {
  paymentResponseId: string;
  status: string;
  detail: string;
  qrCodeBase64: string;
  qrCode: string;
  paymentMethodId: string;
  paymentApi: 'MERCADO_PAGO' | 'BARTE_API';
  captureDate: string;
  creationDate: string;
  secondsRemaining: number;
};

export type IOrderPaymentStatus =
  | 'CANCELED'
  | 'PENDING_CANCEL'
  | 'CONFIRMED'
  | 'PENDING_PAYMENT'
  | 'PENDING_ANTIFRAUD'
  | 'PAYMENT_ERROR'
  | 'PAYMENT_REJECTED'
  | 'PAYMENT_EXPIRED'
  | 'PENDING_CONFIRMATION'
  | 'BOOKING_ERROR';

export type OrderPayment = {
  id?: string;
  status?: IOrderPaymentStatus;
  method: 'CREDIT_CARD' | 'PIX' | 'EXTERNAL' | 'TRIPCASH';
  paymentApi?: string;
  token?: string;
  issuerId?: string;
  currency?: string;
  currencySymbol?: string;
  paymentMethodId: 'mastercard' | 'visa' | 'amex' | 'elo' | 'pix';
  transactionAmount: number;
  transactionAmountRefunded?: number;
  transactionAmountOriginal?: number;
  feeAmount?: number;
  installmentAmount?: number;
  installments?: number;
  installmentsRate?: number;
  payer?: Payer;
  payment?: OrderPayment;
  paymentResponse?: OrderPaymentResponse;
  authorizationCode?: string;
  description?: string;
  usedTripcash?: {
    value: number;
    formattedValue: string;
  };
};

export type Order = {
  id: string;
  status: StatusType;
  sentDate: string;
  orderCode: string;
  price: OrderPrice;
  payment: OrderPayment;
  paymentMethod: 'CREDIT_CARD' | 'PIX';
  paymentResponse: OrderPaymentResponse;
  itens: OrderItem[];
  customer: Customer;
  cancellationDate?: string;
  refundDate?: string;
  lang: string;
  cancellationInfo: {
    infos: string[];
    isCancellable: boolean;
  };
  tripcashInfo: {
    formatedValue: string;
    value: number;
  };
};
export type OrderResponse = {
  content: Order[];
  pageable: {
    sort: [];
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  sort: {
    direction: 'DESC' | 'ASC';
    property: string;
    ignoreCase: boolean;
    nullHandling: string;
    ascending: boolean;
    descending: boolean;
  }[];
  first: boolean;
  size: number;
  number: number;
  numberOfElements: number;
  empty: boolean;
};

export type OrderFilteredPayload = {
  page: number;
  size: number;
  customerMail?: string;
  customerName?: string;
  orderCode?: string;
  reference?: string;
  reservationCode?: string;
  payerMail?: string;
  payerName?: string;
  guestName?: string;
  hotelCode?: string;
  sentDateStart?: string;
  sentDateEnd?: string;
};

export const defaultOrderFilteredPayload = {
  page: 0,
  size: 10,
  customerMail: '',
  customerName: '',
  orderCode: '',
  reference: '',
  reservationCode: '',
  payerMail: '',
  payerName: '',
  guestName: '',
  hotelCode: '',
  sentDateStart: '',
  sentDateEnd: ''
};
