export type AdminPrivilege = {
  id?: string;
  name: string;
};

export type AdminProfile = {
  id?: string;
  name: string;
  privileges: AdminPrivilege[];
};

export type Admin = {
  id?: string;
  name: string;
  email: string;
  password?: string;
  passwordAgain?: string;
  enabled: boolean;
  profiles: AdminProfile[];
};

export type AdminUserSession = {
  id?: string;
  name: string;
  email: string;
  profiles: string[];
  privileges: string[];
};
