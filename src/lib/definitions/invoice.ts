export type Invoice = {
  idNfe: string;
  codPedido: string;
  codReserva: string;
  numeroNfe: string;
  status: string;
  dataEmissao: string;
  competencia: string;
  descricao: string;
  caminhoXml: string;
  urlDanfse: string;
};

export type InvoiceResponse = {
  content: Invoice[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: {
      direction: 'DESC' | 'ASC';
      property: string;
      ignoreCase: boolean;
      descending: boolean;
      ascending: boolean;
    };
    offset: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  sort: {
    direction: 'DESC' | 'ASC';
    property: string;
    ignoreCase: boolean;
    descending: boolean;
    ascending: boolean;
  };
  numberOfElements: number;
  first: boolean;
  empty: boolean;
};
