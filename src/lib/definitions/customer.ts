type CustomerAddress = {
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  postalCode?: string;
  city?: string;
  uf?: string;
  country?: string;
};

export type CustomerRegisterPayload = {
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  phone: {
    ddi: string;
    areaCode: string;
    number: string;
  };
  address?: CustomerAddress;
  email: string;
  password: string;
  passwordAgain?: string;
  lang: string;
};

export type CustomerRegisterResponse = {
  id: string;
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  phone: {
    ddi: string;
    areaCode: string;
    number: string;
  };
  email: string;
  validate: boolean;
  address?: string;
};

export type Customer = {
  id: string;
  name: string;
  surname: string;
  documentNumber: string;
  documentType: string;
  gender?: string;
  birthday?: string;
  phone: {
    ddi: string;
    areaCode: string;
    number: string;
  };
  email: string;
  address?: CustomerAddress;
  verified?: boolean;
};

export type CustomerResponse = {
  content: Customer[];
  pageable: {
    sort: [];
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  sort: {
    direction: 'DESC' | 'ASC';
    property: string;
    ignoreCase: boolean;
    nullHandling: string;
    ascending: boolean;
    descending: boolean;
  }[];
  first: boolean;
  size: number;
  number: number;
  numberOfElements: number;
  empty: boolean;
};
