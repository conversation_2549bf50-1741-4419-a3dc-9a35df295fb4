'use server';

import { verifySession } from '@/actions/dal';
import { TripcashPayload, TripcashResponse } from '@/lib/definitions/tripcash';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const createTripcash = async (
  payload: TripcashPayload
): Promise<{ data?: TripcashResponse; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/tripcash`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar tripcash'
      };
    }

    return {
      error: 'Falha ao criar tripcash'
    };
  }

  return {
    data: await response.json()
  };
};

export const getTripcash = async (
  id: string
): Promise<{ data?: TripcashResponse; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/tripcash/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver tripcash'
      };
    }

    return {
      error: 'Falha ao buscar tripcash'
    };
  }

  return {
    data: await response.json()
  };
};

export const getTripcashs = async (): Promise<{
  data?: TripcashResponse[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/tripcash`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver tripcashs'
      };
    }

    return {
      error: 'Falha ao buscar tripcashs'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateTripcash = async (
  payload: TripcashPayload
): Promise<{ data?: TripcashResponse; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/tripcash`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar tripcash'
      };
    }

    return {
      error: 'Falha ao atualizar tripcash'
    };
  }

  return {
    data: await response.json()
  };
};
