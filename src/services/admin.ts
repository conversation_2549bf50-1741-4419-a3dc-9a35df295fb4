'use server';

import { verifySession } from '@/actions/dal';
import { Admin, AdminProfile, AdminUserSession } from '@/lib/definitions/admin';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const createAdmin = async (
  payload: Admin
): Promise<{ data?: Admin; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar administradores'
      };
    }

    return {
      error: 'Falha ao criar administrador'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateAdmin = async (
  payload: Admin
): Promise<{ data?: Admin; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/users`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar administradores'
      };
    }

    return {
      error: 'Falha ao atualizar administrador'
    };
  }

  return {
    data: await response.json()
  };
};

export async function getUserByToken(
  token: string
): Promise<{ data?: AdminUserSession; error?: string }> {
  const response = await fetch(`${API_URL}/user/admin-session`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar usuário pelo token'
    };
  }

  return {
    data: await response.json()
  };
}

export async function getAdmins(): Promise<{ data?: Admin[]; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/users`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver os administradores'
      };
    }

    return {
      error: 'Falha ao buscar administradores'
    };
  }

  return {
    data: await response.json()
  };
}

export const createProfile = async (
  profile: AdminProfile
): Promise<{ data?: AdminProfile; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/profile`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(profile)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar perfis'
      };
    }
    return {
      error: 'Falha ao criar perfil'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProfiles = async (): Promise<{
  data?: AdminProfile[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/profile`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver os perfis'
      };
    }
    return {
      error: 'Falha ao buscar perfis'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateProfile = async (
  profile: AdminProfile
): Promise<{ data?: AdminProfile; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/profile`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(profile)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar perfis'
      };
    }

    return {
      error: 'Falha ao atualizar perfil'
    };
  }

  return {
    data: await response.json()
  };
};

export async function getPrivileges(): Promise<{
  data?: any[];
  error?: string;
}> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/privilege`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar privilégios'
    };
  }

  return {
    data: await response.json()
  };
}
