'use server';

import { verifySession } from '@/actions/dal';
import { Provider } from '@/lib/definitions/provider';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const createProvider = async (
  payload: Provider
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar provedores'
      };
    }

    return {
      error: 'Falha ao criar provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateProvider = async (
  payload: Provider
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar provedores'
      };
    }

    return {
      error: 'Falha ao atualizar provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProvider = async (
  id: string
): Promise<{ data?: Provider; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver provedores'
      };
    }

    return {
      error: 'Falha ao buscar provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProviders = async (): Promise<{
  data?: Provider[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver provedores'
      };
    }

    return {
      error: 'Falha ao buscar provedores'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProviderCodes = async (): Promise<{
  data?: string[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider/codes`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar códigos de provedores'
    };
  }

  return {
    data: await response.json()
  };
};
