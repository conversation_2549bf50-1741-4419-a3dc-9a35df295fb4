'use server';

import { verifySession } from '@/actions/dal';
import {
  Order,
  OrderFilteredPayload,
  OrderResponse
} from '@/lib/definitions/order';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export async function getOrder(
  orderId: string
): Promise<{ data?: Order; error?: string }> {
  const session = await verifySession();
  if (!session) throw new Error('Unauthorized');

  const response = await fetch(`${API_URL}/admin/order/${orderId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar pedido'
    };
  }

  return {
    data: await response.json()
  };
}

export async function getOrders(
  page: number,
  size: number
): Promise<{ data?: OrderResponse; error?: string }> {
  const session = await verifySession();
  if (!session) throw new Error('Unauthorized');

  const response = await fetch(
    `${API_URL}/admin/order?page=${page}&size=${size}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`
      }
    }
  );

  if (!response.ok) {
    return {
      error: 'Falha ao buscar pedidos'
    };
  }

  return {
    data: await response.json()
  };
}

export async function getOrdersFiltered(
  payload: OrderFilteredPayload
): Promise<{ data?: OrderResponse; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/order/filtered`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    return {
      error: 'Falha ao buscar pedidos filtrados'
    };
  }

  return {
    data: await response.json()
  };
}

export async function cancelOrder(params: {
  orderId: string;
  message: string;
}): Promise<{ data?: boolean; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(
    `${API_URL}/admin/order/cancel/${params.orderId}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`
      },
      body: JSON.stringify({ message: params.message })
    }
  );

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para cancelar pedidos'
      };
    }

    const responseBody = await response.json();
    if (responseBody.message) {
      return {
        error: responseBody.message
      };
    }

    return {
      error: 'Falha ao cancelar pedido'
    };
  }

  return {
    data: response.ok
  };
}

export async function refundOrder(params: {
  orderId: string;
  amount: number;
  message: string;
}): Promise<{ data?: boolean; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(
    `${API_URL}/admin/order/refund/${params.orderId}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session!.token}`
      },
      body: JSON.stringify({ amount: params.amount, message: params.message })
    }
  );

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para reembolsar pedidos'
      };
    }

    const responseBody = await response.json();
    if (responseBody.message) {
      return {
        error: responseBody.message
      };
    }

    return {
      error: 'Falha ao reembolsar pedido'
    };
  }

  return {
    data: response.ok
  };
}

export async function localRefundOrder(params: {
  orderId: string;
  message: string;
}): Promise<{ data?: boolean; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(
    `${API_URL}/admin/order/refundLocal/${params.orderId}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`
      },
      body: JSON.stringify({ message: params.message })
    }
  );

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para marcar pedidos como reembolsados'
      };
    }

    const responseBody = await response.json();
    if (responseBody.message) {
      return {
        error: responseBody.message
      };
    }

    return {
      error: 'Falha ao marcar pedido como reembolsado'
    };
  }

  return {
    data: response.ok
  };
}

export async function localCancelOrder(params: {
  orderId: string;
  message: string;
}): Promise<{ data?: boolean; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(
    `${API_URL}/admin/order/cancelLocal/${params.orderId}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`
      },
      body: JSON.stringify({ message: params.message })
    }
  );

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para marcar pedidos como cancelados'
      };
    }

    const responseBody = await response.json();
    if (responseBody.message) {
      return {
        error: responseBody.message
      };
    }

    return {
      error: 'Falha ao marcar pedido como cancelado'
    };
  }

  return {
    data: response.ok
  };
}
