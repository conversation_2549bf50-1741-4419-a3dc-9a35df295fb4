'use server';

const TIMI_SERVICE_URL = process.env.NEXT_PUBLIC_TIMI_SERVICE_URL;
const TRIVAGO_SERVICE_URL = process.env.NEXT_PUBLIC_TRIVAGO_SERVICE_URL;
const TRIVAGO_SERVICE_CONTROLLER_URL =
  process.env.NEXT_PUBLIC_TRIVAGO_SERVICE_CONTROLLER_URL;
const INTELLIGENCE_SERVICE_URL =
  process.env.NEXT_PUBLIC_INTELLIGENCE_SERVICE_URL;

export const trivagoServicePing = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(`${TRIVAGO_SERVICE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const getTrivagoServiceLogs = async (): Promise<{
  data?: string;
  error?: string;
}> => {
  try {
    const response = await fetch(`${TRIVAGO_SERVICE_CONTROLLER_URL}/logs`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return {
        error: 'Falha ao buscar logs'
      };
    }

    const data = await response.json();

    return {
      data: data.data.content
    };
  } catch (error) {
    return {
      error: 'Falha ao buscar logs'
    };
  }
};

export const trivagoServiceStart = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(
      `${TRIVAGO_SERVICE_CONTROLLER_URL}/trivago-service/start`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const trivagoServiceRestart = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(
      `${TRIVAGO_SERVICE_CONTROLLER_URL}/trivago-service/restart`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const timiServicePing = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(`${TIMI_SERVICE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const intelligenceServicePing = async (): Promise<{
  data: boolean;
}> => {
  try {
    const response = await fetch(`${INTELLIGENCE_SERVICE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return {
        data: false
      };
    }

    return {
      data: response.ok
    };
  } catch (error) {
    return {
      data: false
    };
  }
};

export const run = async (
  formData: FormData
): Promise<{
  data?: boolean;
  error?: string;
}> => {
  try {
    const response = await fetch(`${TIMI_SERVICE_URL}/run`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      return {
        error: 'Falha ao executar'
      };
    }

    return {
      data: response.ok
    };
  } catch (error: any) {
    return {
      error: error.message
    };
  }
};

export const stopRun = async (
  runId: string
): Promise<{
  data?: boolean;
  error?: string;
}> => {
  try {
    const response = await fetch(`${TIMI_SERVICE_URL}/run/stop/${runId}`, {
      method: 'POST'
    });

    if (!response.ok) {
      return {
        error: 'Falha ao parar execução'
      };
    }

    return {
      data: response.ok
    };
  } catch (error: any) {
    return {
      error: error.message
    };
  }
};
