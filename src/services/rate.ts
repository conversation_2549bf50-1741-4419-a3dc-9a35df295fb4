'use server';

import { verifySession } from '@/actions/dal';
import { ChannelAndProvidersOptions, Rate } from '@/lib/definitions/rate';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const getChannelsAndProviders = async (): Promise<{
  data?: ChannelAndProvidersOptions;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/rate/channels-and-providers`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para buscar canais e provedores'
      };
    }

    return {
      error: 'Falha ao buscar canais e provedores'
    };
  }

  return {
    data: await response.json()
  };
};

export const getRates = async (): Promise<{
  data?: Rate[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/rate`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para buscar taxas'
      };
    }

    return {
      error: 'Falha ao buscar taxas'
    };
  }

  return {
    data: await response.json()
  };
};

export const createRate = async (
  payload: Rate
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/rate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar taxas'
      };
    }

    return {
      error: 'Falha ao criar taxa'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateRate = async (
  payload: Rate
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/rate`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar taxas'
      };
    }

    return {
      error: 'Falha ao atualizar taxa'
    };
  }

  return {
    data: await response.json()
  };
};

export const deleteRate = async (
  id: string
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/rate/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para deletar taxas'
      };
    }

    return {
      error: 'Falha ao deletar taxa'
    };
  }

  return {
    data: await response.json()
  };
};
