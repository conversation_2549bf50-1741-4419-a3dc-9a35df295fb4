'use server';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export type LoginCredentials = {
  email: string;
  password: string;
};

export const login = async (credentials: LoginCredentials): Promise<string> => {
  const response = await fetch(`${API_URL}/auth/admin`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Basic ${Buffer.from(`${credentials.email}:${credentials.password}`).toString('base64')}`
    }
  });

  if (!response.ok) {
    throw new Error('Falha ao logar');
  }

  return response.text();
};
