'use server';

import { verifySession } from '@/actions/dal';
import { ProviderAccount } from '@/lib/definitions/provider-account';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const createProviderAccount = async (
  payload: ProviderAccount
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider-account`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar contas de provedor'
      };
    }

    return {
      error: 'Falha ao criar conta de provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateProviderAccount = async (
  payload: ProviderAccount
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider-account`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar contas de provedor'
      };
    }

    return {
      error: 'Falha ao atualizar conta de provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProviderAccount = async (
  id: string
): Promise<{ data?: ProviderAccount; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider-account/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver contas de provedor'
      };
    }

    return {
      error: 'Falha ao buscar conta de provedor'
    };
  }

  return {
    data: await response.json()
  };
};

export const getProvidersAccounts = async (): Promise<{
  data?: ProviderAccount[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/provider-account`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver contas de provedor'
      };
    }

    return {
      error: 'Falha ao buscar contas de provedor'
    };
  }

  return {
    data: await response.json()
  };
};
