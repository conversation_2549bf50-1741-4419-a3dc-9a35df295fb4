'use server';

import { verifySession } from '@/actions/dal';
import { Channel } from '@/lib/definitions/channel';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const createChannel = async (
  payload: Channel
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/channel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar canais'
      };
    }

    return {
      error: 'Falha ao criar canal'
    };
  }

  return {
    data: await response.json()
  };
};

export const updateChannel = async (
  payload: Channel
): Promise<{ data?: any; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/channel`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar canais'
      };
    }

    return {
      error: 'Falha ao atualizar canal'
    };
  }

  return {
    data: await response.json()
  };
};

export const getChannel = async (
  id: string
): Promise<{ data?: Channel; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/channel/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver canais'
      };
    }

    return {
      error: 'Falha ao buscar canal'
    };
  }

  return {
    data: await response.json()
  };
};

export const getChannels = async (): Promise<{
  data?: Channel[];
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/channel`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver canais'
      };
    }

    return {
      error: 'Falha ao buscar canais'
    };
  }

  return {
    data: await response.json()
  };
};
