'use server';

import { verifySession } from '@/actions/dal';
import {
  Customer,
  CustomerRegisterPayload,
  CustomerRegisterResponse,
  CustomerResponse
} from '@/lib/definitions/customer';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export async function getCustomers(
  page: number,
  size: number
): Promise<{ data?: CustomerResponse; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(
    `${API_URL}/admin/customer?page=${page}&size=${size}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`
      }
    }
  );

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver os clientes'
      };
    }

    return {
      error: 'Falha ao buscar clientes'
    };
  }

  return {
    data: await response.json()
  };
}

export async function getCustomer(
  id: string
): Promise<{ data?: Customer; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/customer/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver os clientes'
      };
    }

    return {
      error: 'Falha ao buscar cliente'
    };
  }

  return {
    data: await response.json()
  };
}

export async function updateCustomer(
  payload: Customer
): Promise<{ data?: Customer; error?: string }> {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/customer/${payload.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para editar clientes'
      };
    }

    return {
      error: 'Falha ao atualizar cliente'
    };
  }

  return {
    data: await response.json()
  };
}

export async function createCustomer(
  payload: CustomerRegisterPayload
): Promise<{ data?: CustomerRegisterResponse; error?: string }> {
  const response = await fetch(`${API_URL}/customer`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para criar clientes'
      };
    }

    return {
      error: 'Falha ao criar cliente'
    };
  }

  return {
    data: await response.json()
  };
}

export async function recoveryPasswordRequest(
  email: string
): Promise<{ data?: any; error?: string }> {
  const response = await fetch(`${API_URL}/user/send-reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, lang: 'PT_BR' })
  });

  if (!response.ok) {
    return {
      error: 'Falha ao solicitar recuperação de senha'
    };
  }

  return {
    data: await response.json()
  };
}
