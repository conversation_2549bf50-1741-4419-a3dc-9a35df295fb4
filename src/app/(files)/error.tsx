'use client';

import { Button } from '@ourtrip/ui';
import { Prohibit } from '@phosphor-icons/react';

const Error = ({
  error,
  reset
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) => {
  return (
    <div className='w-full h-full flex flex-col justify-center items-center min-h-screen bg-gray-50'>
      <Prohibit size={28} className='text-gray-500' />
      <h1 className='text-lg font-semibold text-gray-900 mt-3'>
        Acesso Negado
      </h1>
      <p className='text-gray-600 mb-4'>
        Você não tem permissão para acessar esta página.
      </p>
      <Button onClick={reset}>Tentar novamente</Button>
    </div>
  );
};

export default Error;
