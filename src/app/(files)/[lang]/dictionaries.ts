import 'server-only';

const dictionaries = {
  EN_US: () =>
    import('./dictionaries/en_US.json').then(module => module.default),
  ES_ES: () =>
    import('./dictionaries/es_ES.json').then(module => module.default),
  PT_BR: () =>
    import('./dictionaries/pt_BR.json').then(module => module.default),
  PT_PT: () =>
    import('./dictionaries/pt_PT.json').then(module => module.default)
};

export const getDictionary = async (
  locale: 'EN_US' | 'ES_ES' | 'PT_BR' | 'PT_PT'
) => dictionaries[locale]();
