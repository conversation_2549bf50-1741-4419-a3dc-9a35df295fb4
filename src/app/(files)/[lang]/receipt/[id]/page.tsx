import ReceiptFile from '@/components/receipt';
import { getOrder } from '@/services/order';
import { getDictionary } from '../../dictionaries';

const Receipt = async ({
  params
}: {
  params: Promise<{
    id: string;
    responsable: string;
    lang: 'EN_US' | 'ES_ES' | 'PT_BR' | 'PT_PT';
  }>;
}) => {
  const { id, responsable, lang } = await params;
  const orderResponse = await getOrder(id);
  const dictionary = await getDictionary(lang);

  if (orderResponse.error) {
    throw new Error(orderResponse.error);
  }
  const order = orderResponse.data!;

  return (
    <ReceiptFile
      order={order}
      responsable={responsable}
      dictionary={dictionary}
      lang={lang}
    />
  );
};

export default Receipt;
