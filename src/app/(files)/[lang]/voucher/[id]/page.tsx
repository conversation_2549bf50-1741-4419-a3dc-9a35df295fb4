import { getOrder } from '@/services/order';
import VoucherFile from '@/components/voucher';
import { getDictionary } from '../../dictionaries';

const Voucher = async ({
  params
}: {
  params: Promise<{ id: string; lang: 'EN_US' | 'ES_ES' | 'PT_BR' | 'PT_PT' }>;
}) => {
  const { id, lang } = await params;
  const orderResponse = await getOrder(id);
  const dictionary = await getDictionary(lang.toUpperCase() as any);

  if (orderResponse.error) {
    throw new Error(orderResponse.error);
  }
  const order = orderResponse.data!;

  return <VoucherFile order={order} dictionary={dictionary} lang={lang} />;
};

export default Voucher;
