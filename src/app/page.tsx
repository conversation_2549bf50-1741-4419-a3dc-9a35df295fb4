'use client';

import { useActionState } from 'react';
import { Button, Input } from '@ourtrip/ui';
import { signin } from '@/actions/auth';
import { UserCircle } from '@phosphor-icons/react';

const Login = () => {
  const [state, action, pending] = useActionState(signin, undefined);

  return (
    <div className='w-[100vw] h-[100vh] bg-gray-100 flex items-center justify-center'>
      <form
        action={action}
        className='w-[350px] bg-white flex flex-col justify-center rounded-default p-6'
      >
        <UserCircle size={32} weight='duotone' />
        <h1 className='text-xl font-semibold text-primary-900 mb-1 mt-2'>
          Bem vindo(a) de volta!
        </h1>
        <h3 className='text-sm text-gray-500 mb-4'>
          Faça login para acessar sua conta de administrador
        </h3>
        <Input
          name='email'
          color='gray'
          placeholder='Email'
          type='email'
          className='mb-1'
        />
        {state?.errors?.email && (
          <p className='text-red-500 text-sm mb-4'>{state.errors.email}</p>
        )}
        <Input
          name='password'
          color='gray'
          placeholder='Senha'
          type='password'
          className='mb-2'
        />
        {state?.errors?.password && (
          <p className='text-red-500 text-sm mb-4'>{state.errors.password}</p>
        )}
        <Button
          type='submit'
          color='primary'
          className='w-full font-semibold'
          loading={pending}
        >
          Entrar
        </Button>
      </form>
    </div>
  );
};

export default Login;
