import { Toaster } from '@/components/sonner';
import './globals.css';
import type { Metadata } from 'next';
import { Jo<PERSON> } from 'next/font/google';

const jost = Jost({
  variable: '--font-jost',
  subsets: ['latin']
});

export const metadata: Metadata = {
  title: 'OurTrip | Admin',
  description: 'Administração do sistema OurTrip',
  publisher: 'OurTrip',
  category: 'Business',
  robots: {
    index: false,
    follow: false
  }
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='pt-br'>
      <body className={`${jost.variable} antialiased`}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
