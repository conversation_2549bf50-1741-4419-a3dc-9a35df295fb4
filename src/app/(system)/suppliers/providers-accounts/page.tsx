import ProviderAccountActions from '@/components/provider-account-actions';
import Provider<PERSON>ccountHeader from '@/components/provider-account-header';
import ProviderAccountStatus from '@/components/provider-account-status';
import RefreshButton from '@/components/refresh-button';
import Table from '@/components/table';
import { getProvidersAccounts } from '@/services/providers-accounts';
import { tableColumns } from './loading';

const ProvidersAccounts = async () => {
  const providersAccountsResponse = await getProvidersAccounts();

  if (providersAccountsResponse.error) {
    throw new Error(providersAccountsResponse.error);
  }
  const providersAccounts = providersAccountsResponse.data!;

  const tableItems = providersAccounts
    ?.sort((a, b) => a.providerCode.localeCompare(b.providerCode))
    .map((providerAccount, index) => ({
      id: providerAccount.id,
      code: (
        <p className='text-primary-900 text-sm font-medium'>
          {providerAccount.code}
        </p>
      ),
      url: <p className='text-gray-500 text-sm'>{providerAccount.url}</p>,
      main: (
        <p className='text-gray-500 text-sm'>
          {providerAccount.main ? 'Sim' : 'Não'}
        </p>
      ),
      status: <ProviderAccountStatus providerAccount={providerAccount} />,
      actions: <ProviderAccountActions providerAccount={providerAccount} />,
      separator:
        providersAccounts[index + 1]?.providerCode !==
        providerAccount.providerCode ? (
          <p className='text-sm text-gray-500'>
            {providersAccounts[index + 1]?.providerCode}
          </p>
        ) : null
    }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Contas de Provedores</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <ProviderAccountHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default ProvidersAccounts;
