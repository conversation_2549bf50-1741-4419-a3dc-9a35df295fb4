import ProviderStatus from '@/components/provider-status';
import ProvidersActions from '@/components/providers-actions';
import ProvidersHeader from '@/components/providers-header';
import RefreshButton from '@/components/refresh-button';
import Table from '@/components/table';
import { getProviders } from '@/services/provider';
import { tableColumns } from './loading';

const Providers = async () => {
  const providersResponse = await getProviders();

  if (providersResponse.error) {
    throw new Error(providersResponse.error);
  }
  const providers = providersResponse.data!;

  const tableItems: any[] = providers?.map(provider => ({
    id: provider.id,
    name: (
      <p className='text-primary-900 text-sm font-medium'>{provider.name}</p>
    ),
    code: <p className='text-gray-500 text-sm'>{provider.code}</p>,
    hotels: <p className='text-gray-500 text-sm'>{provider.amountHotels}</p>,
    destinations: (
      <p className='text-gray-500 text-sm'>{provider.amountDestinations}</p>
    ),
    status: <ProviderStatus provider={provider} />,
    actions: <ProvidersActions provider={provider} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Provedores</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <ProvidersHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default Providers;
