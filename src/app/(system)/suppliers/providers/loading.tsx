import Table, { TableColumn } from '@/components/table';

export const tableColumns: TableColumn<string, any>[] = [
  {
    id: 'name',
    name: 'Nome'
  },
  {
    id: 'code',
    name: '<PERSON><PERSON><PERSON>'
  },
  {
    id: 'hotels',
    name: '<PERSON><PERSON><PERSON>'
  },
  {
    id: 'destinations',
    name: 'Destino<PERSON>'
  },
  {
    id: 'status',
    name: 'Status'
  },
  {
    id: 'actions',
    name: '<PERSON><PERSON><PERSON><PERSON>'
  }
];

const Loading = () => {
  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2 animate-pulse'>
        <div className='w-32 h-7 bg-gray-200 rounded-inner' />
        <div className='w-6 h-6 bg-gray-200 rounded-full' />
      </div>
      <div className='w-full flex items-start gap-4'>
        <div className='w-full flex flex-col gap-3'>
          <div className='w-full flex justify-between items-end'>
            <div className='w-20 h-8 bg-gray-200 animate-pulse rounded-inner' />
            <div className='w-44 h-5 bg-gray-200 animate-pulse rounded-inner' />
          </div>
          <Table columns={tableColumns} items={[]} loading={true} />
        </div>
      </div>
    </div>
  );
};

export default Loading;
