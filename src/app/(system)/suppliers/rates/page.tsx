import RateActions from '@/components/rate-actions';
import RateHeader from '@/components/rate-header';
import RefreshButton from '@/components/refresh-button';
import Table from '@/components/table';
import { getRates } from '@/services/rate';
import { tableColumns } from './loading';

const Rates = async () => {
  const ratesResponse = await getRates();

  if (ratesResponse.error) {
    throw new Error(ratesResponse.error);
  }
  const rates = ratesResponse.data!;

  const tableItems: any[] = rates?.map(rate => ({
    id: rate.id,
    markup: <p className='text-primary-900 font-medium'>{rate.markup}</p>,
    tax: <p className='text-primary-900 font-medium'>{rate.tax}</p>,
    provider: <p className='text-gray-500 text-sm'>{rate.provider}</p>,
    providerAccount: (
      <p className='text-gray-500 text-sm'>{rate.providerAccount}</p>
    ),
    channel: <p className='text-gray-500 text-sm'>{rate.channel}</p>,
    actions: <RateActions rate={rate} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Taxas</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <RateHeader />
        <Table columns={tableColumns} items={tableItems} />
      </div>
    </div>
  );
};

export default Rates;
