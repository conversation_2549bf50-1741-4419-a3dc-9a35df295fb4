import SupplierPanel from '@/components/supplier-panel';
import { getChannels } from '@/services/channel';
import { getProviders } from '@/services/provider';
import { getProvidersAccounts } from '@/services/providers-accounts';
import { getRates } from '@/services/rate';
import { ReactFlowProvider } from '@xyflow/react';

const Editor = async () => {
  // const channels = await getChannels();
  // const providers = await getProviders();
  // const providersAccounts = await getProvidersAccounts();
  // const rates = await getRates();

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      {/* <ReactFlowProvider>
        <SupplierPanel
          channels={channels}
          providers={providers}
          providersAccounts={providersAccounts}
          rates={rates}
        />
      </ReactFlowProvider> */}
    </div>
  );
};

export default Editor;
