import ChannelsActions from '@/components/channels-actions';
import ChannelsHeader from '@/components/channels-header';
import ChannelsStatus from '@/components/channels-status';
import RefreshButton from '@/components/refresh-button';
import Table from '@/components/table';
import { getChannels } from '@/services/channel';
import { tableColumns } from './loading';

const Channels = async () => {
  const channelsResponse = await getChannels();

  if (channelsResponse.error) {
    throw new Error(channelsResponse.error);
  }
  const channels = channelsResponse.data!;

  const tableItems: any[] = channels?.map(channel => ({
    id: channel.id,
    name: (
      <p className='text-primary-900 text-sm font-medium'>{channel.name}</p>
    ),
    code: <p className='text-gray-500 text-sm'>{channel.code}</p>,
    status: <ChannelsStatus channel={channel} />,
    actions: <ChannelsActions channel={channel} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Canais</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <ChannelsHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default Channels;
