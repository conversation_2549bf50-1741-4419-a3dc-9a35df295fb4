import RefreshButton from '@/components/refresh-button';
import OrderHeader from '@/components/order-header';
import OrderFooter from '@/components/order-footer';
import OrderStatus from '@/components/order-status';
import OrderActions from '@/components/order-table-actions';
import { convertStringToDateWithMask } from '@/lib/utils/date';
import PaymentDetails from '@/components/payment-details';
import OrderValue from '@/components/order-value';
import { getOrdersFiltered } from '@/services/order';
import Table from '@/components/table';
import { OrderFilteredPayload } from '@/lib/definitions/order';
import { tableColumns } from './loading';
import { toast } from 'sonner';

interface OrdersProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

const Orders = async ({ searchParams }: OrdersProps) => {
  const params = await searchParams;

  const payload = {
    page: parseInt(params.page?.toString() ?? '0', 10),
    size: parseInt(params.size?.toString() ?? '10', 10),
    customerName: params.customerName || '',
    customerMail: params.customerMail || '',
    orderCode: params.orderCode || '',
    reservationCode: params.reservationCode || '',
    hotelCode: params.hotelCode || '',
    payerName: params.payerName || '',
    payerMail: params.payerMail || '',
    guestName: params.guestName || '',
    reference: params.reference || '',
    sentDateStart: params.sentDateStart || '',
    sentDateEnd: params.sentDateEnd || ''
  };

  const response = await getOrdersFiltered(payload as OrderFilteredPayload);

  if (response.error) {
    throw new Error(response.error);
  }
  const orders = response.data!;

  const tableItems: any[] =
    orders?.content.map(order => ({
      id: order.id,
      code: (
        <p className='text-gray-500 text-sm font-medium'>{order.orderCode}</p>
      ),
      value: (
        <OrderValue
          value={order.price.finalPrice.formattedValue}
          currency={order.price.currency}
          currencySymbol={order.price.currencySymbol}
          installments={order.price.installments}
          installmentAmount={order.price.installmentAmount.value}
          method={order.payment.method}
        />
      ),
      paymentMethod: (
        <PaymentDetails
          method={order.payment.method}
          currency={order.price.currency}
          currencySymbol={order.price.currencySymbol}
          transactionAmount={order.payment.transactionAmount}
          paymentMethodId={order.payment.paymentMethodId}
          installmentAmount={order.payment.installmentAmount}
        />
      ),
      date: (
        <p className='text-sm text-gray-500'>
          {convertStringToDateWithMask(order.sentDate, 'dd/MM/yyyy HH:mm')}
        </p>
      ),
      status: <OrderStatus status={order.status} />,
      actions: <OrderActions order={order} />
    })) ?? [];

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Pedidos</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <OrderHeader orders={orders} />
        <Table columns={tableColumns} items={tableItems} loading={false} />
        <OrderFooter orders={orders} />
      </div>
    </div>
  );
};

export default Orders;
