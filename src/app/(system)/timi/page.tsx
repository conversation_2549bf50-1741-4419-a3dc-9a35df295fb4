import RefreshButton from '@/components/refresh-button';
import TimiP<PERSON> from '@/components/timi-play';
import <PERSON>iRunList from '@/components/timi-run-list';
import TimiRunning from '@/components/timi-running';
import TimiStatus from '@/components/timi-status';
import StatCard from '@/components/stat-card';
import { createClient } from '@/services/supabase';
import {
  intelligenceServicePing,
  timiServicePing,
  trivagoServicePing
} from '@/services/timi';

const Timi = async () => {
  const supabase = await createClient();

  const { data: runs } = await supabase
    .from('vw_last_20_run_summary')
    .select(
      `
      id,
      filters,
      created_at,
      ended_at,
      hotels_count,
      processed_hotels_count,
      total_changed_hotels,
      total_hotels_with_lower_price,
      total_successful_hotels,
      success_rate_percent,
      total_unsuccessful_hotels
    `
    )
    .order('created_at', { ascending: false });

  const { data: running } = await supabase
    .from('run')
    .select('*')
    .is('ended_at', null)
    .single();

  const { data: trivagoStatus } = await trivagoServicePing();
  const { data: timiStatus } = await timiServicePing();
  const { data: intelligenceStatus } = await intelligenceServicePing();

  const { data } = await supabase
    .from('dashboard_summary')
    .select('*')
    .single();

  const {
    total_runs: totalRuns,
    successful_changes: successfulChanges,
    failed_changes: failedChanges,
    todays_runs: todayRuns,
    monitored_hotels: monitoredHotels,
    avg_hotels_per_run: avgHotelsPerRun,
    success_rate: successRate
  } = data!;

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Timi</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex gap-6'>
        <div className='w-full flex flex-col'>
          <div className='flex flex-col gap-4'>
            <div className='flex gap-4'>
              <StatCard value={totalRuns || 0} label='Execuções totais' />
              <StatCard value={`${successRate}%`} label='Taxa de sucesso' />
              <StatCard value={todayRuns || 0} label='Execuções hoje' />
              <StatCard value={monitoredHotels || 0} label='Hotéis atuados' />
            </div>
            <div className='flex gap-4'>
              <StatCard
                value={avgHotelsPerRun || 0}
                label='Média Hotéis/Execução'
              />
              <StatCard
                value={successfulChanges || 0}
                label='Alterações com sucesso'
              />
              <StatCard
                value={failedChanges || 0}
                label='Alterações com falha'
              />
            </div>
          </div>
          <p className='text-sm text-gray-500 mt-3 mb-1'>Execuções</p>
          <TimiRunList runs={runs ?? []} />
        </div>
        <div className='w-[350px] flex flex-col gap-3 flex-none'>
          <TimiStatus
            trivagoStatus={trivagoStatus}
            timiStatus={timiStatus}
            intelligenceStatus={intelligenceStatus}
          />
          {running && <TimiRunning runId={running!.id} />}
          {!running && (
            <TimiPlay
              trivagoStatus={trivagoStatus}
              timiStatus={timiStatus}
              intelligenceStatus={intelligenceStatus}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Timi;
