import Table from '@/components/table';
import RefreshButton from '@/components/refresh-button';
import { getCustomers } from '@/services/customer';
import CustomersEmail from '@/components/customers-email';
import CustomersStatus from '@/components/customers-status';
import CustomersActions from '@/components/customers-actions';
import CustomersHeader from '@/components/customers-header';
import CustomersFooter from '@/components/customers-footer';
import { tableColumns } from './loading';

type CustomersProps = {
  searchParams: Promise<{
    [key: string]: string | string[] | undefined;
  }>;
};

const Customers = async ({ searchParams }: CustomersProps) => {
  const params = await searchParams;

  const payload = {
    page: parseInt(params.page?.toString() ?? '0', 10),
    size: parseInt(params.size?.toString() ?? '10', 10)
  };

  const response = await getCustomers(payload.page, payload.size);

  if (response.error) {
    throw new Error(response.error);
  }
  const customers = response.data!;

  const tableItems: any[] =
    customers?.content?.map(customer => ({
      id: customer.id,
      name: (
        <p className='text-primary-900 text-sm font-medium'>{customer.name}</p>
      ),
      email: <CustomersEmail customer={customer} />,
      document: (
        <p className='text-gray-500 text-sm'>{customer.documentNumber}</p>
      ),
      status: <CustomersStatus customer={customer} />,
      phone: (
        <p className='text-gray-500 text-sm'>
          {customer.phone.ddi} {customer.phone.areaCode} {customer.phone.number}
        </p>
      ),
      actions: <CustomersActions customer={customer} />
    })) ?? [];

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Clientes</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <CustomersHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
        <CustomersFooter customers={customers} />
      </div>
    </div>
  );
};

export default Customers;
