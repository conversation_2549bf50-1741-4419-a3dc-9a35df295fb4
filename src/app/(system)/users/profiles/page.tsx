import Table from '@/components/table';
import RefreshButton from '@/components/refresh-button';
import { tableColumns } from './loading';
import ProfileActions from '@/components/profile-actions';
import { getProfiles } from '@/services/admin';
import ProfileHeader from '@/components/profile-header';

const ProfilesPage = async () => {
  const response = await getProfiles();

  if (response.error) {
    throw new Error(response.error);
  }
  const profiles = response.data!;

  const tableItems: any[] = profiles.map(profile => ({
    name: (
      <p className='text-primary-900 text-sm font-medium'>{profile.name}</p>
    ),
    privileges: (
      <p className='text-gray-500 text-sm'>
        {profile.privileges.slice(0, 3).map(privilege => (
          <span key={privilege.id} className='mr-1'>
            {privilege.name}
          </span>
        ))}
      </p>
    ),
    actions: <ProfileActions profile={profile} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Perfis</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <ProfileHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default ProfilesPage;
