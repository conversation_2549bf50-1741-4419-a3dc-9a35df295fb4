import Table from '@/components/table';
import RefreshButton from '@/components/refresh-button';
import { getAdmins } from '@/services/admin';
import AdminsActions from '@/components/admins-actions';
import AdminsHeader from '@/components/admins-header';
import { tableColumns } from './loading';
import AdminStatus from '@/components/admin-status';

const AdminsPage = async () => {
  const response = await getAdmins();

  if (response.error) {
    throw new Error(response.error);
  }
  const admins = response.data!;

  const tableItems: any[] = admins.map(admin => ({
    name: <p className='text-primary-900 text-sm font-medium'>{admin.name}</p>,
    email: <p className='text-gray-500 text-sm'>{admin.email}</p>,
    enabled: <AdminStatus admin={admin} />,
    profiles: (
      <p className='text-gray-500 text-sm'>
        {admin.profiles.map(profile => (
          <span key={profile.id}>{profile.name}</span>
        ))}
      </p>
    ),
    actions: <AdminsActions admin={admin} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Administradores</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <AdminsHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default AdminsPage;
