import Table from '@/components/table';

const Loading = () => {
  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-center gap-2 animate-pulse'>
        <div className='w-32 h-6 bg-gray-200 rounded' />
        <div className='w-6 h-6 bg-gray-200 rounded-full' />
      </div>
      <div className='flex items-start gap-6'>
        <div className='flex flex-col gap-3'>
          <div className='flex items-start gap-6'>
            <div className='flex-4 flex gap-2'>
              {[0, 1, 2].map((_, idx) => (
                <div
                  key={idx}
                  className='w-full flex flex-col items-center gap-2 bg-white rounded-default p-6 animate-pulse'
                >
                  <div className='w-8 h-8 bg-gray-200 rounded-inner' />
                  <div className='w-24 h-4 bg-gray-200 rounded-inner' />
                  <div className='w-44 h-7 bg-gray-100 animate-pulse rounded-inner mt-2' />
                </div>
              ))}
            </div>
          </div>
          <Table
            columns={[
              { id: 0, name: 'Código do Pedido' },
              { id: 1, name: 'Data de Emissão' },
              { id: 2, name: 'Competência' },
              { id: 3, name: 'Erro' }
            ]}
            items={[]}
            loading={true}
          />
        </div>
        <div className='w-[360px] bg-white rounded-default p-3 animate-pulse'>
          <div className='w-20 h-6 bg-gray-200 rounded-inner mb-2' />
          <div className='w-full h-20 flex justify-center border-2 border-dashed border-gray-200 rounded-inner p-6'>
            <div className='w-28 h-5 bg-gray-200 animate-pulse rounded-inner' />
          </div>
          <div className='w-full h-10 bg-gray-200 rounded-inner mt-2' />
        </div>
      </div>
    </div>
  );
};

export default Loading;
