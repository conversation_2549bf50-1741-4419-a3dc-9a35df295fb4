import RefreshButton from '@/components/refresh-button';
import { getTripcashs } from '@/services/tripcash';
import { convertStringToUTCDate } from '@/lib/utils/date';
import { format } from 'date-fns';
import TripcashStatus from '@/components/tripcash-status';
import TripcashActions from '@/components/tripcash-actions';
import TripcashHeader from '@/components/tripcash-header';
import Table from '@/components/table';
import { tableColumns } from './loading';

const Tripcash = async () => {
  const tripcashsResponse = await getTripcashs();

  if (tripcashsResponse.error) {
    throw new Error(tripcashsResponse.error);
  }
  const tripcashs = tripcashsResponse.data!;

  const tableItems: any[] = tripcashs.map(tripcash => ({
    name: (
      <p className='text-primary-900 text-sm font-medium'>{tripcash.title}</p>
    ),
    description: (
      <p className='text-gray-500 text-sm'>{tripcash.description}</p>
    ),
    type: <p className='text-gray-500 text-sm'>{tripcash.type}</p>,
    expiresAt: (
      <p className='text-gray-500 text-sm'>
        {format(convertStringToUTCDate(tripcash.expiresAt), 'dd/MM/yyyy')}
      </p>
    ),
    percentage: (
      <p className='text-gray-500 text-sm'>
        {Math.trunc(tripcash.percent * 100)}%
      </p>
    ),
    status: <TripcashStatus active={tripcash.active} />,
    actions: <TripcashActions tripcash={tripcash} />
  }));

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Tripcash</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <TripcashHeader />
        <Table columns={tableColumns} items={tableItems} loading={false} />
      </div>
    </div>
  );
};

export default Tripcash;
