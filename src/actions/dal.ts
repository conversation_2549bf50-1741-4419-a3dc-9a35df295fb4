'use server';

import { cache } from 'react';
import { cookies } from 'next/headers';
import { decrypt } from '@/actions/encrypt';

const verifySession = cache(async () => {
  const cookie = (await cookies()).get('session')?.value;
  if (!cookie) return null;

  const session = await decrypt(cookie);
  if (!session.id || !session.name || !session.email) {
    return null;
  }

  return {
    userId: session.id,
    userName: session.name,
    userEmail: session.email,
    profiles: session.profiles,
    privileges: session.privileges,
    token: session.token,
    expiresAt: session.expiresAt
  };
});

const getUser = cache(async () => {
  try {
    const session = await verifySession();

    if (!session) throw new Error('Session not found');
    if (!session.userId) throw new Error('User not found');

    const user = {
      id: session.userId as string,
      name: session.userName as string,
      email: session.userEmail as string,
      profiles: session.profiles as string[],
      privileges: session.privileges as string[]
    };

    return user;
  } catch (error) {
    return null;
  }
});

export { getUser, verifySession };
