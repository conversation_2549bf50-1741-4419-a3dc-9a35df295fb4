'use server';

import { login } from '@/services/auth';
import { createSession, deleteSession } from './session';
import { redirect } from 'next/navigation';
import { FormState, LoginFormSchema } from '@/lib/definitions/login';
import { getUserByToken } from '@/services/admin';
import { AdminUserSession } from '@/lib/definitions/admin';

const signin = async (
  _: FormState,
  formData: FormData
): Promise<
  | {
      errors: Record<string, string | string[] | undefined>;
      message?: string;
    }
  | undefined
> => {
  let token;
  let user: AdminUserSession | undefined | null;

  const validatedFields = LoginFormSchema.safeParse({
    email: formData.get('email'),
    password: formData.get('password')
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: 'Missing Fields. Failed to Sign In.'
    };
  }

  try {
    token = await login({
      email: validatedFields.data.email,
      password: validatedFields.data.password
    });

    user = (await getUserByToken(token)).data;

    if (token) {
      await createSession(user, token);
    }
  } catch (error) {
    return {
      errors: {
        password: 'Invalid email or password'
      }
    };
  } finally {
    if (!token) {
      return {
        errors: {
          password: 'Invalid email or password'
        }
      };
    }
    if (!user) {
      return {
        errors: {
          password: 'Invalid email or password'
        }
      };
    }

    redirect('/dashboard');
  }
};

const logout = async () => {
  deleteSession();
  redirect('/');
};

export { signin, logout };
