'use server';

import { cookies } from 'next/headers';
import { encrypt } from './encrypt';

export type SessionPayload = {
  id: string;
  name: string;
  email: string;
  token: string;
  expiresAt: Date;
};

const createSession = async (user: any, token: string) => {
  try {
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000);
    const session = await encrypt({
      ...{
        id: user.id,
        name: user.name,
        email: user.email,
        profiles: user.profiles || [],
        privileges: user.privileges || []
      },
      token,
      expiresAt
    });
    const cookieStore = await cookies();

    cookieStore.set('session', session, {
      // httpOnly: true,
      // secure: true,
      expires: expiresAt,
      sameSite: 'lax',
      path: '/'
    });
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error('Failed to create session');
  }
};

const deleteSession = async () => {
  const cookieStore = await cookies();
  cookieStore.delete('session');
};

export { createSession, deleteSession };
