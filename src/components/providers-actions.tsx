'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Provider } from '@/lib/definitions/provider';
import { updateProvider } from '@/services/provider';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Pause,
  Pencil,
  Play
} from '@phosphor-icons/react';
import ProviderSheet from './provider-sheet';
import { toast } from 'sonner';

const ProvidersActions = ({ provider }: { provider: Provider }) => {
  const router = useRouter();
  const [isToggling, setIsToggling] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const handleUpdateProvider = async (provider: Provider) => {
    setIsEditing(true);
    const response = await updateProvider(provider);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Provedor atualizado com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };

  const handleToggleActive = async () => {
    setIsToggling(true);
    const response = await updateProvider({
      ...provider,
      enabled: !provider.enabled
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Provedor atualizado com sucesso');
      router.refresh();
    }

    setIsToggling(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col gap-1'>
            <ProviderSheet
              provider={provider}
              submit={handleUpdateProvider}
              loading={isEditing}
            >
              <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
                <Pencil />
                <p className='text-sm text-primary-900 font-medium'>Editar</p>
              </div>
            </ProviderSheet>
            <div
              onClick={handleToggleActive}
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
            >
              {isToggling ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : !provider.enabled ? (
                <Play />
              ) : (
                <Pause />
              )}
              <p className='text-sm text-primary-900 font-medium'>
                {!provider.enabled ? 'Habilitar' : 'Desabilitar'}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ProvidersActions;
