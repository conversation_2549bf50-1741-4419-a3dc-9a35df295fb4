'use client';

import { TripcashPayload } from '@/lib/definitions/tripcash';
import { createTripcash } from '@/services/tripcash';
import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import TripcashSheet from './tripcash-sheet';
import { toast } from 'sonner';

const TripcashHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (tripcash: TripcashPayload) => {
    setIsCreating(true);
    const response = await createTripcash(tripcash);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Tripcash criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <TripcashSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar Tripcash
        </Button>
      </TripcashSheet>
    </div>
  );
};

export default TripcashHeader;
