'use client';

import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import RateSheet from './rate-sheet';
import { Rate } from '@/lib/definitions/rate';
import { createRate } from '@/services/rate';
import { toast } from 'sonner';

const RateHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (rate: Rate) => {
    setIsCreating(true);
    const response = await createRate(rate);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Taxa criada com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <RateSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar taxa
        </Button>
      </RateSheet>
    </div>
  );
};

export default RateHeader;
