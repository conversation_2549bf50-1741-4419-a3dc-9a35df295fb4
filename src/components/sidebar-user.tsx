'use client';

import { logout } from '@/actions/auth';
import { Button } from '@ourtrip/ui';
import { SignOut } from '@phosphor-icons/react/dist/ssr';

const SidebarUser = ({
  user
}: {
  user: { id: string; name: string; email: string; profiles: string[] };
}) => {
  if (!user) {
    return null;
  }

  return (
    <div className='flex justify-between items-center'>
      <div className='flex items-center gap-3'>
        <div className='w-12 h-12 flex-none rounded-full bg-gray-100 text-primary-500 flex items-center justify-center font-medium'>
          {user.name.charAt(0)}
        </div>
        <div className='flex flex-col justify-center items-start mt-1'>
          <h3 className='font-semibold leading-4'>
            {`${user.name.split(' ')[0] || ''} ${user.name.split(' ')[1] || ''}`}
          </h3>
          <p className='text-sm text-gray-500'>{user.profiles[0] || ''}</p>
        </div>
      </div>
      <div className='flex gap-2'>
        <Button color='white' size='icon' onClick={logout} title='Sair'>
          <SignOut weight='bold' size={18} className='text-gray-500' />
        </Button>
      </div>
    </div>
  );
};

export default SidebarUser;
