'use client';

import { Channel } from '@/lib/definitions/channel';
import { Badge } from '@ourtrip/ui';
import { Check, X } from '@phosphor-icons/react';

const ChannelsStatus = ({ channel }: { channel: Channel }) => {
  return (
    <Badge
      className='flex w-min'
      type={channel.enabled ? 'success' : 'danger'}
      icon={channel.enabled ? <Check /> : <X />}
      size='small'
    >
      {channel.enabled ? 'Ativo' : 'Inativo'}
    </Badge>
  );
};

export default ChannelsStatus;
