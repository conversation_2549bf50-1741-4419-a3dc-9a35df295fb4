'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { CustomerResponse } from '@/lib/definitions/customer';
import { Pagination } from '@ourtrip/ui';

const CustomersFooter = ({
  customers
}: {
  customers: CustomerResponse | null;
}) => {
  const router = useRouter();
  const params = useSearchParams();

  const handleChange = (newPage: number) => {
    const newParams = new URLSearchParams(params.toString());

    newParams.set('page', (newPage - 1).toString());
    router.push(`?${newParams.toString()}`);
  };

  return (
    <Pagination
      page={params.get('page') ? parseInt(params.get('page') || '0') + 1 : 1}
      total={customers ? customers.totalPages : 0}
      onChange={handleChange}
    />
  );
};

export default CustomersFooter;
