'use client';

import { Channel } from '@/lib/definitions/channel';
import {
  Button,
  Input,
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@ourtrip/ui';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const ChannelSheet = ({
  children,
  channel,
  submit,
  loading
}: {
  children: React.ReactNode;
  channel?: Channel;
  submit: (data: Channel) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<Channel>({
    defaultValues: {
      id: channel?.id || '',
      code: channel?.code || '',
      name: channel?.name || '',
      enabled: channel?.enabled || false
    }
  });

  const onSubmit = async (data: Channel) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>{channel ? 'Editar Canal' : 'Criar Canal'}</SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-4 mt-2'>
            <Input
              placeholder='Código *'
              {...register('code', { required: true })}
              error={errors.code?.message}
              color='gray'
            />
            <Input
              placeholder='Nome *'
              {...register('name', { required: true })}
              error={errors.name?.message}
              color='gray'
            />
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {channel ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default ChannelSheet;
