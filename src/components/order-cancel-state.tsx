'use client';

import { Order } from '@/lib/definitions/order';
import { convertStringToDateWithMask } from '@/lib/utils/date';

const OrderCancelState = ({ order }: { order: Order }) => {
  return (
    <div className='rounded-default bg-white flex gap-2 p-4'>
      <div className='w-full'>
        <p className='text-sm text-gray-500'>Reembolsado em:</p>
        <h3 className='text-primary-900 font-medium'>
          {order.refundDate
            ? convertStringToDateWithMask(order.refundDate, 'dd/MM/yyyy HH:mm')
            : '-'}
        </h3>
      </div>
      <div className='w-full'>
        <p className='text-sm text-gray-500'>Cancelado em:</p>
        <h3 className='text-primary-900 font-medium'>
          {order.cancellationDate
            ? convertStringToDateWithMask(
                order.cancellationDate,
                'dd/MM/yyyy HH:mm'
              )
            : '-'}
        </h3>
      </div>
    </div>
  );
};

export default OrderCancelState;
