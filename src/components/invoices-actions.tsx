'use client';

import { Invoice } from '@/lib/definitions/invoice';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Download,
  Eye,
  ReceiptX,
  X
} from '@phosphor-icons/react';
import { useState } from 'react';

const InvoicesActions = ({
  invoice,
  onSelect,
  onDownload
}: {
  invoice: Invoice;
  onSelect: (id: string) => Promise<void>;
  onDownload: (id: string) => Promise<void>;
}) => {
  const [isLoadingView, setIsLoadingView] = useState<boolean>(false);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const handleDownload = async (id: string) => {
    setIsDownloading(true);
    await onDownload(id);
    setIsDownloading(false);
  };

  const handleView = async (id: string) => {
    setIsLoadingView(true);
    await onSelect(id);
    setIsLoadingView(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col'>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={() => handleView(invoice.idNfe)}
            >
              {isLoadingView ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : (
                <Eye />
              )}
              <p className='text-sm text-primary-900 font-medium'>Ver</p>
            </div>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={() => handleDownload(invoice.idNfe)}
            >
              {isDownloading ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : (
                <Download />
              )}
              <p className='text-sm text-primary-900 font-medium'>Baixar</p>
            </div>
            <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
              <ReceiptX />
              <p className='text-sm text-primary-900 font-medium'>Cancelar</p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default InvoicesActions;
