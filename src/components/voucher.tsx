'use client';

import dynamic from 'next/dynamic';
import { Order } from '@/lib/definitions/order';
import {
  convertStringToFormatedDateByLocale,
  getDateFNSLocale
} from '@/lib/utils/date';
import { Divider, Stars } from '@ourtrip/ui';
import { Ticket } from '@phosphor-icons/react';
import { convertNumberToCurrency } from '@/lib/utils/currency';

const GeneratePDF = dynamic(() => import('@/components/generate-pdf'), {
  ssr: false
});

const VoucherFile = ({
  order,
  dictionary,
  lang
}: {
  order: Order;
  dictionary: any;
  lang: 'EN_US' | 'ES_ES' | 'PT_BR' | 'PT_PT';
}) => {
  return (
    <div className='w-[794px] flex flex-col items-center bg-gray-100 p-12 mx-auto voucher'>
      <GeneratePDF fileName='voucher' />
      <img
        width={200}
        height={50}
        src='/horizontal_icon_text_blue.png'
        alt='Logo'
        className='object-contain my-[15px]'
      />
      <div className='w-full flex flex-col items-center p-[50px] bg-white mt-[25px] rounded-default'>
        <Ticket weight='duotone' size={75} className='text-primary-900' />
        <h1 className='text-primary-900 text-[24px] mt-[15px] leading-normal'>
          {dictionary?.voucher?.title || 'Voucher de hospedagem'}
        </h1>
        <div className='mt-[35px] text-primary-900 text-[20px]'>
          {dictionary?.voucher?.reservationNumber || 'Número da reserva:'}{' '}
          <span className='font-semibold'>
            {order.itens[0].booking.reference}
          </span>
        </div>
        <div className='flex justify-center gap-[15px] my-[25px]'>
          <div className='flex flex-col text-right gap-[5px]'>
            <p className='text-gray-500'>
              {dictionary?.voucher?.checkin || 'Check-in'}
            </p>
            <h3 className='text-primary-900 text-[16px] font-medium'>
              {convertStringToFormatedDateByLocale(
                order.itens[0].booking.checkin,
                getDateFNSLocale(lang ?? 'pt_BR')
              )}
            </h3>
          </div>
          <Divider orientation='vertical' />
          <div className='flex flex-col gap-[5px]'>
            <p className='text-gray-500'>
              {dictionary?.voucher?.checkout || 'Check-out'}
            </p>
            <h3 className='text-primary-900 text-[16px] font-medium'>
              {convertStringToFormatedDateByLocale(
                order.itens[0].booking.checkout,
                getDateFNSLocale(lang ?? 'pt_BR')
              )}
            </h3>
          </div>
        </div>
        {order.status === 'CONFIRMED' && (
          <div className='text-danger-500 w-[90%]'>
            {dictionary?.voucher?.paidReservationAlert ||
              '* A sua reserva está paga, não há necessidade de pagamento referente às diárias cobertas pelo período deste voucher no hotel.'}
          </div>
        )}
        <div className='w-[90%] flex flex-col gap-[10px] p-[25px] border border-gray-200 rounded-default mt-[25px]'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.hotel || 'Hotel'}
            </p>
            <div className='text-end flex flex-col items-end text-[14px]'>
              <Stars rate={parseFloat(order.itens[0].booking.hotel.stars)} />
              {order.itens[0].booking.hotel.name}{' '}
            </div>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.address || 'Endereço'}
            </p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.address}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.phone || 'Telefone'}
            </p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.phone}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.lodgingType || 'Tipo de Hospedagem'}
            </p>
            <p className='text-end flex flex-col items-end text-[14px]'>
              {order.itens[0].booking.hotel.type}
            </p>
          </div>
        </div>
        {order.itens[0].booking.rooms.map(room => (
          <div
            className='w-[90%] flex flex-col p-[25px] border border-gray-200 rounded-default mt-[25px]'
            key={room.accommodationId}
          >
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.room} {room.accommodationIndex + 1}
            </p>
            <div className='mb-[15px] font-medium text-[14px]'>
              {room.accommodationName}
            </div>
            <p className='text-gray-500 text-[14px]'>
              {dictionary?.voucher?.guests || 'Hóspedes'}
            </p>
            <div className='flex flex-col gap-[10px] mt-[5px] mb-[15px]'>
              {room.guests.map(guest => (
                <div
                  className='w-full flex justify-between font-medium'
                  key={guest.id}
                >
                  <p className='text-primary-900 text-[14px]'>
                    {guest.name} {guest.surname}
                  </p>
                  <p className='text-end flex flex-col items-end text-[14px]'>
                    {guest.child
                      ? `${guest.age} ${dictionary?.voucher?.yearsOld || 'anos'}`
                      : guest.document}
                  </p>
                </div>
              ))}
            </div>
            <Divider orientation='horizontal' />
            <div className='w-full flex justify-between my-[15px]'>
              <p className='text-gray-500 text-[14px]'>
                {dictionary?.voucher?.mealPlanType || 'Tipo de Refeição'}
              </p>
              <p className='text-end flex flex-col items-end text-[14px]'>
                {room.mealPlanDisplay}
              </p>
            </div>
            {room.additionalTaxes && (
              <>
                <Divider orientation='horizontal' />
                <div className='flex flex-col my-4'>
                  <p className='text-gray-500 text-[14px] '>
                    {dictionary.voucher?.additionalTaxes || 'Taxas Adicionais'}
                  </p>
                  <p className='text-sm italic my-3 text-gray-500'>
                    {dictionary?.voucher?.additionalTaxesDescription ||
                      'Os valores a serem pagos na propriedade foram calculados com base na cotação atual para facilitar a visualização. O valor correto será cobrado em EUR na propriedade.'}
                  </p>
                  <div className='flex justify-between'>
                    <p className='text-sm font-medium'>
                      {dictionary?.voucher?.additionalTaxesNotIncluded ||
                        'Pago na propriedade'}
                    </p>
                    <p className='text-sm font-medium'>
                      ({room.additionalTaxes.originalCurrency}{' '}
                      {convertNumberToCurrency(
                        room.additionalTaxes.originalCurrency,
                        room.additionalTaxes.originalAmountNotIncluded,
                        lang
                      )}
                      ) {room.additionalTaxes.currencySymbol}{' '}
                      {convertNumberToCurrency(
                        room.additionalTaxes.currency,
                        room.additionalTaxes.amountNotIncluded,
                        lang
                      )}
                    </p>
                  </div>
                </div>
              </>
            )}
            {room.rateComments && room.rateComments.length > 0 && (
              <>
                <Divider orientation='horizontal' />
                <p className='text-gray-500 text-[14px] mt-4'>
                  {dictionary?.voucher?.observations || 'Observações'}
                </p>
                <ul className='w-full'>
                  {room.rateComments?.map(rateComment => (
                    <li
                      className='float-right text-gray-500 my-[10px] text-[14px] ml-[25px]'
                      key={rateComment}
                    >
                      {rateComment}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        ))}
        <div className='w-[90%] text-center text-gray-500'>
          <p className='my-[25px] text-[14px]'>
            {order.itens[0]?.booking?.additionalInformation}
          </p>
        </div>
      </div>
    </div>
  );
};

export default VoucherFile;
