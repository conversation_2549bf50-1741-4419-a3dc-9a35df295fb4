'use client';

import { Customer } from '@/lib/definitions/customer';
import { Badge } from '@ourtrip/ui';
import { Check, Clock } from '@phosphor-icons/react';

const CustomersStatus = ({ customer }: { customer: Customer }) => {
  return (
    <div>
      {Boolean(customer.address!) ? (
        <Badge
          size='small'
          type='success'
          icon={<Check />}
          className='flex w-min'
        >
          Completo
        </Badge>
      ) : (
        <Badge
          size='small'
          type='warning'
          icon={<Clock />}
          className='flex w-min'
        >
          Incompleto
        </Badge>
      )}
    </div>
  );
};

export default CustomersStatus;
