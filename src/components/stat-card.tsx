'use client';

interface StatCardProps {
  value: string | number;
  label: string;
}

const StatCard = ({ value, label }: StatCardProps) => {
  return (
    <div
      className={`w-full bg-white rounded-default px-3 py-5 flex flex-col items-center justify-center`}
    >
      <p className={`text-xl font-semibold text-primary-900`}>{value}</p>
      <p className='text-sm text-gray-500 text-center'>{label}</p>
    </div>
  );
};

export default StatCard;
