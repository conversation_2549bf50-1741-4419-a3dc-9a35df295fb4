'use client';

import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { createProviderAccount } from '@/services/providers-accounts';
import { ProviderAccount } from '@/lib/definitions/provider-account';
import ProviderAccountSheet from './provider-account-sheet';
import { toast } from 'sonner';

const ProviderAccountHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (providerAccount: ProviderAccount) => {
    setIsCreating(true);
    const response = await createProviderAccount(providerAccount);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Conta de provedor criada com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <ProviderAccountSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar conta de provedor
        </Button>
      </ProviderAccountSheet>
    </div>
  );
};

export default ProviderAccountHeader;
