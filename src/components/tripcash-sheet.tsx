'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { TripcashPayload, TripcashResponse } from '@/lib/definitions/tripcash';
import { Minus, Plus } from '@phosphor-icons/react';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@ourtrip/ui';

const TripcashSheet = ({
  children,
  tripcash,
  submit,
  loading = false
}: {
  children: React.ReactNode;
  tripcash?: TripcashResponse;
  submit: (data: TripcashPayload) => Promise<void>;
  loading?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { register, handleSubmit, reset, watch, setValue } =
    useForm<TripcashPayload>({
      defaultValues: tripcash
        ? {
            ...tripcash,
            destinations: tripcash.destinations.length
              ? tripcash.destinations
              : [''],
            hotels: tripcash.hotels.length ? tripcash.hotels : ['']
          }
        : {
            destinations: [''],
            hotels: ['']
          }
    });

  const type = watch('type');
  const destinations = watch('destinations');
  const hotels = watch('hotels');

  const handleFormSubmit = async (data: TripcashPayload) => {
    const tripcashData: TripcashPayload = {
      ...tripcash,
      ...data
    };

    Object.assign(tripcashData, {
      hotels:
        data.type === 'DESTINATIONS' || data.type === 'ALL' ? [] : data.hotels,
      destinations:
        data.type === 'HOTELS' || data.type === 'ALL' ? [] : data.destinations
    });

    await submit(tripcashData);
    setIsOpen(false);
    reset();
  };

  return (
    <Sheet
      open={isOpen}
      onOpenChange={open => {
        setIsOpen(open);
        if (!open) reset();
      }}
    >
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent>
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <SheetHeader>
            <SheetTitle>
              {tripcash ? 'Editar Tripcash' : 'Novo Tripcash'}
            </SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-2 mt-2'>
            <div className='flex gap-2'>
              <Input
                label='Nome *'
                placeholder='Ex: Verão'
                {...register('title', {
                  required: 'Campo obrigatório'
                })}
                color='gray'
              />
              <Input
                label='Descrição *'
                placeholder='Ex: Desconto de 10% em hotéis'
                {...register('description', {
                  required: 'Campo obrigatório'
                })}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Input
                label='Valor do desconto'
                placeholder='Ex: 10'
                type='number'
                {...register('value', {
                  valueAsNumber: true
                })}
                color='gray'
              />
              <Input
                label='Porcentagem'
                placeholder='Ex: 10'
                type='number'
                step={0.01}
                {...register('percent', {
                  valueAsNumber: true,
                  min: {
                    value: 0,
                    message: 'Valor mínimo é 0'
                  },
                  max: {
                    value: 1,
                    message: 'Valor máximo é 1'
                  }
                })}
                color='gray'
              />
            </div>
            <Input
              label='% no Tempo'
              placeholder='Ex: 10'
              type='number'
              step={0.01}
              {...register('percentOnTime', {
                valueAsNumber: true,
                min: {
                  value: 0,
                  message: 'Valor mínimo é 0'
                },
                max: {
                  value: 1,
                  message: 'Valor máximo é 1'
                }
              })}
              color='gray'
            />
            <Input
              label='Expiração'
              type='date'
              {...register('expiresAt', {
                required: 'Campo obrigatório'
              })}
              color='gray'
            />
            <p className='text-gray-500 text-sm mt-1'>Tipo</p>
            <Select
              {...register('type', {
                required: 'Campo obrigatório'
              })}
              defaultValue={tripcash?.type}
              onValueChange={value => {
                setValue('type', value as any);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder='Selecione o tipo' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='ALL'>Todos</SelectItem>
                <SelectItem value='HOTELS'>Hotéis</SelectItem>
                <SelectItem value='DESTINATIONS'>Destinos</SelectItem>
              </SelectContent>
            </Select>
            {(type === 'DESTINATIONS' || type === 'HOTELS') &&
              Array.from({
                length:
                  type === 'DESTINATIONS' ? destinations.length : hotels.length
              }).map((_, index) => (
                <div className='flex gap-2' key={index}>
                  <Input
                    key={`${type === 'DESTINATIONS' ? 'destination' : 'hotel'}-${index}`}
                    placeholder={`ID do ${
                      type === 'DESTINATIONS' ? 'destino' : 'hotel'
                    }`}
                    {...register(
                      `${type === 'DESTINATIONS' ? 'destinations' : 'hotels'}.${index}`
                    )}
                    color='gray'
                  />
                  {index ===
                    (type === 'DESTINATIONS'
                      ? destinations.length
                      : hotels.length) -
                      1 && (
                    <Button
                      color='gray'
                      onClick={() => {
                        const newItems = [
                          ...(type === 'DESTINATIONS' ? destinations : hotels)
                        ];
                        newItems[index + 1] = '';
                        setValue(
                          type === 'DESTINATIONS' ? 'destinations' : 'hotels',
                          newItems
                        );
                      }}
                    >
                      <Plus />
                    </Button>
                  )}
                  {(type === 'DESTINATIONS'
                    ? destinations.length
                    : hotels.length) > 1 && (
                    <Button
                      color='gray'
                      onClick={() => {
                        const newItems = [
                          ...(type === 'DESTINATIONS' ? destinations : hotels)
                        ];
                        newItems.splice(index, 1);
                        setValue(
                          type === 'DESTINATIONS' ? 'destinations' : 'hotels',
                          newItems
                        );
                      }}
                    >
                      <Minus />
                    </Button>
                  )}
                </div>
              ))}
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {tripcash ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default TripcashSheet;
