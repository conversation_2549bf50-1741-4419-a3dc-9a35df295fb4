'use client';

import { Rate } from '@/lib/definitions/rate';
import { getChannelsAndProviders } from '@/services/rate';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Sheet,
  SheetClose,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@ourtrip/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const RateSheet = ({
  children,
  rate,
  submit,
  loading
}: {
  children: React.ReactNode;
  rate?: Rate;
  submit: (data: Rate) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [providers, setProviders] = useState<string[]>([]);
  const [channels, setChannels] = useState<string[]>([]);
  const [providerAccounts, setProviderAccounts] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors }
  } = useForm<Rate>({
    defaultValues: {
      id: rate?.id || '',
      markup: rate?.markup || 0,
      tax: rate?.tax || 0,
      provider: rate?.provider || '',
      providerAccount: rate?.providerAccount || '',
      channel: rate?.channel || ''
    }
  });

  const fetchRates = async () => {
    const configsResponse = await getChannelsAndProviders();

    if (configsResponse.error) {
      toast.error(configsResponse.error);
    } else {
      const { providers, channels, providerAccounts } = configsResponse.data!;
      setProviders(providers);
      setChannels(channels);
      setProviderAccounts(providerAccounts);
    }
  };

  const onSubmit = async (data: Rate) => {
    if (data.providerAccount === '') {
      data.providerAccount = null;
    }
    if (data.provider === '') {
      data.provider = null;
    }
    if (data.channel === '') {
      data.channel = null;
    }

    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  useEffect(() => {
    fetchRates();
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>Adicionar Taxa</SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-4 mt-2'>
            <div className='flex gap-2'>
              <Input
                type='number'
                placeholder='Informe um Markup'
                label='Markup *'
                {...register('markup', { required: true })}
                error={errors.markup?.message}
                color='gray'
              />
              <Input
                type='number'
                placeholder='Informe uma Taxa'
                label='Taxa *'
                {...register('tax', { required: true })}
                error={errors.tax?.message}
                color='gray'
              />
            </div>
            <div>
              <p className='text-gray-500 text-sm mt-2'>Provedor</p>
              <Select
                {...register('provider')}
                onValueChange={value => setValue('provider', value)}
                defaultValue={rate?.provider || ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Selecione o Provedor' />
                </SelectTrigger>
                <SelectContent>
                  {providers.map(provider => (
                    <SelectItem key={provider} value={provider}>
                      {provider}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <p className='text-gray-500 text-sm mt-2'>Conta de Provedor</p>
              <Select
                {...register('providerAccount')}
                onValueChange={value => setValue('providerAccount', value)}
                defaultValue={rate?.providerAccount || ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Selecione a Conta de Provedor' />
                </SelectTrigger>
                <SelectContent>
                  {providerAccounts.map(providerAccount => (
                    <SelectItem key={providerAccount} value={providerAccount}>
                      {providerAccount}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <p className='text-gray-500 text-sm mt-2'>Canal</p>
            <Select
              {...register('channel')}
              onValueChange={value => setValue('channel', value)}
              defaultValue={rate?.channel || ''}
            >
              <SelectTrigger>
                <SelectValue placeholder='Selecione o Canal' />
              </SelectTrigger>
              <SelectContent>
                {channels.map(channel => (
                  <SelectItem key={channel} value={channel}>
                    {channel}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {rate ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default RateSheet;
