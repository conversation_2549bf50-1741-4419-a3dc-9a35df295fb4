'use client';

import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { OrderResponse } from '@/lib/definitions/order';
import { Pagination } from '@ourtrip/ui';

const OrderFooter = ({ orders }: { orders: OrderResponse | null }) => {
  const router = useRouter();
  const params = useSearchParams();

  const handleChange = (newPage: number) => {
    const newParams = new URLSearchParams(params.toString());

    newParams.set('page', (newPage - 1).toString());
    router.push(`?${newParams.toString()}`);
  };

  return (
    <Pagination
      page={params.get('page') ? parseInt(params.get('page') || '0') + 1 : 1}
      total={orders ? orders.totalPages : 0}
      onChange={handleChange}
    />
  );
};

export default OrderFooter;
