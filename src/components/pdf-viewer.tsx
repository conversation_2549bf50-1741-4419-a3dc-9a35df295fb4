'use client';

import { Document, Page, pdfjs } from 'react-pdf';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

export function PDFViewer({ file }: { file: A<PERSON>yBuffer }) {
  if (file.detached) return;

  return (
    <Document file={file}>
      <Page
        pageNumber={1}
        renderTextLayer={false}
        renderAnnotationLayer={false}
      />
    </Document>
  );
}
