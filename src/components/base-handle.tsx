import { forwardRef } from 'react';
import { Handle, HandleProps } from '@xyflow/react';

import { cn } from '@/lib/utils/classes';

export type BaseHandleProps = HandleProps;

export const BaseHandle = forwardRef<HTMLDivElement, BaseHandleProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Handle
        ref={ref}
        {...props}
        className={cn(
          'h-3 w-3 rounded-full border border-gray-200 bg-gray-100 transition',
          className
        )}
        {...props}
      >
        {children}
      </Handle>
    );
  }
);

BaseHandle.displayName = 'BaseHandle';
