'use client';

import { createCustomer } from '@/services/customer';
import { CustomerRegisterPayload } from '@/lib/definitions/customer';
import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import CustomerSheet from './customer-sheet';
import { useState } from 'react';
import { toast } from 'sonner';

const CustomersHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (customer: CustomerRegisterPayload) => {
    setIsCreating(true);

    const response = await createCustomer(customer);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Cliente criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <CustomerSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar conta
        </Button>
      </CustomerSheet>
    </div>
  );
};

export default CustomersHeader;
