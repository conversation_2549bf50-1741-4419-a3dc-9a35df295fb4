'use client';

import dynamic from 'next/dynamic';
import { convertNumberToCurrency } from '@/lib/utils/currency';
import {
  convertStringToFormatedDateByLocale,
  getDateFNSLocale
} from '@/lib/utils/date';
import { Order } from '@/lib/definitions/order';
import { Receipt } from '@phosphor-icons/react';

const GeneratePDF = dynamic(() => import('@/components/generate-pdf'), {
  ssr: false
});

const ReceiptFile = ({
  order,
  responsable,
  dictionary,
  lang
}: {
  order: Order;
  responsable: string;
  dictionary: any;
  lang: 'EN_US' | 'ES_ES' | 'PT_BR' | 'PT_PT';
}) => {
  const { payer } = order.payment;
  const guest = order.itens[0].booking.rooms[0].guests[0];
  const responsableName =
    responsable === 'payer' && payer
      ? `${payer?.firstName} ${payer?.lastName}`
      : `${guest.name} ${guest.surname}`;

  const paymentMethodLabel = {
    CREDIT_CARD:
      order.payment.installments === 1
        ? dictionary?.receipt?.creditCardSinglePayment ||
          'Cartão de crédito à vista'
        : dictionary?.receipt?.creditCardInstallments.replace(
            '{{installments}}',
            order.payment.installments?.toString()
          ) || `"Cartão de crédito em ${order.payment.installments} vezes"`,
    PIX: dictionary?.receipt?.pixSinglePayment || 'Pix à vista',
    TRIPCASH: dictionary?.receipt?.tripcash || 'Tripcash',
    EXTERNAL: dictionary?.receipt?.external || 'Carta de crédito'
  };

  return (
    <div className='w-[794px] flex flex-col items-center bg-gray-100 p-12 mx-auto recibo'>
      <GeneratePDF fileName='recibo' />
      <img
        width={200}
        height={50}
        src='/horizontal_icon_text_blue.png'
        className='object-contain my-4'
      />
      <div className='w-full flex flex-col items-center p-12 bg-white mt-6 rounded-default'>
        <Receipt size={75} className='text-primary-900' />
        <h1 className='text-primary-900 text-xl mt-4 leading-normal'>
          {dictionary?.receipt?.title || 'Recibo de hospedagem'}
        </h1>
        <div className='w-[90%] flex flex-col gap-2 p-6 border border-gray-200 rounded-default mt-6'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.lodging || 'Hospedagem'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.itens[0].booking.hotel.name}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.period || 'Período'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {dictionary?.receipt?.checkin || 'Check-in:'}{' '}
              {convertStringToFormatedDateByLocale(
                order.itens[0].booking.checkin,
                getDateFNSLocale(lang ?? 'pt_BR')
              )}
              <br />
              {dictionary?.receipt?.checkout || 'Check-out:'}{' '}
              {convertStringToFormatedDateByLocale(
                order.itens[0].booking.checkout,
                getDateFNSLocale(lang ?? 'pt_BR')
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.reservationUnderName ||
                'Reserva em nome de'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {responsableName}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.orderNumber || 'Nº do pedido'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.orderCode}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.amount || 'Valor'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.price.currencySymbol}
              {` `}
              {convertNumberToCurrency(
                order.price.currency!,
                order.price.finalPrice.value
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.purchaseDate || 'Data de compra'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {convertStringToFormatedDateByLocale(
                order.sentDate,
                getDateFNSLocale(lang ?? 'pt_BR')
              )}
            </p>
          </div>
        </div>

        <div className='w-[90%] flex flex-col gap-2 p-6 border border-gray-200 rounded-default mt-6'>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.total || 'Total'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {order.price.currencySymbol}
              {` `}
              {convertNumberToCurrency(
                order.price.currency!,
                order.price.finalPrice.value
              )}
            </p>
          </div>
          <div className='w-full flex justify-between'>
            <p className='text-gray-500 text-sm'>
              {dictionary?.receipt?.paymentMethod || 'Forma de pagamento'}
            </p>
            <p className='text-end flex flex-col items-end text-sm'>
              {paymentMethodLabel[order.payment.method]}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiptFile;
