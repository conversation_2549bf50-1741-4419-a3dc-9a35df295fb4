'use client';

import { InvoiceResponse } from '@/lib/definitions/invoice';
import { Pagination } from '@ourtrip/ui';
import { useEffect, useState } from 'react';

const InvoicesFooter = ({
  invoices,
  onPageChange
}: {
  invoices: InvoiceResponse | null;
  onPageChange: (page: number) => void;
}) => {
  const [page, setPage] = useState<number>(0);

  useEffect(() => {
    setPage(invoices?.number || 0);
  }, [invoices]);

  return (
    <Pagination
      page={page + 1}
      total={invoices ? invoices.totalPages : 0}
      onChange={newPage => {
        setPage(newPage - 1);
        onPageChange(newPage - 1);
      }}
    />
  );
};

export default InvoicesFooter;
