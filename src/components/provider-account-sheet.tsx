'use client';

import { ProviderAccount } from '@/lib/definitions/provider-account';
import { getProviderCodes } from '@/services/provider';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Sheet,
  SheetClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON>Trigger
} from '@ourtrip/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const ProviderAccountSheet = ({
  children,
  providerAccount,
  submit,
  loading
}: {
  children: React.ReactNode;
  providerAccount?: ProviderAccount;
  submit: (data: ProviderAccount) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [providers, setProviders] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors }
  } = useForm<ProviderAccount>({
    defaultValues: {
      id: providerAccount?.id || '',
      code: providerAccount?.code || '',
      providerCode: providerAccount?.providerCode || '',
      url: providerAccount?.url || '',
      userName: providerAccount?.userName || '',
      key: providerAccount?.key || '',
      others: providerAccount?.others || '',
      main: providerAccount?.main || false,
      enabled: providerAccount?.enabled || false
    }
  });

  const fetchProvidersCodes = async () => {
    const response = await getProviderCodes();

    if (response.error) {
      toast.error(response.error);
    } else {
      setProviders(response.data!);
    }
  };

  const onSubmit = async (data: ProviderAccount) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  useEffect(() => {
    fetchProvidersCodes();
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>Adicionar Conta de Provedor</SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-4 mt-2'>
            <Select
              disabled={!!providerAccount}
              {...register('providerCode', { required: true })}
              onValueChange={value => setValue('providerCode', value)}
              defaultValue={providerAccount?.providerCode}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder='Selecione o Provedor' />
              </SelectTrigger>
              <SelectContent>
                {providers.map(provider => (
                  <SelectItem key={provider} value={provider}>
                    {provider}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              placeholder='Código *'
              {...register('code', { required: true })}
              disabled={!!providerAccount}
              error={errors.code?.message}
              color='gray'
            />
            <Input
              placeholder='URL *'
              {...register('url', { required: true })}
              error={errors.url?.message}
              color='gray'
            />
            <Input
              placeholder='Nome de Usuário'
              {...register('userName')}
              error={errors.userName?.message}
              color='gray'
            />
            <div className='flex gap-2'>
              <Input
                placeholder='Chave'
                {...register('key')}
                error={errors.key?.message}
                color='gray'
              />
              <Input
                placeholder='Outros'
                {...register('others')}
                error={errors.others?.message}
                color='gray'
              />
            </div>
            <Select
              {...register('main')}
              onValueChange={value => setValue('main', value === 'true')}
              defaultValue={providerAccount?.main ? 'true' : 'false'}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder='Selecione o Provedor' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='true'>Sim</SelectItem>
                <SelectItem value='false'>Não</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {providerAccount ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default ProviderAccountSheet;
