'use client';

import React from 'react';
import {
  defaultOrderFilteredPayload,
  OrderFilteredPayload,
  OrderResponse
} from '@/lib/definitions/order';
import {
  Badge,
  Button,
  Input,
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger
} from '@ourtrip/ui';
import { Sliders, X } from '@phosphor-icons/react';
import { useForm } from 'react-hook-form';
import { useSearchParams, useRouter } from 'next/navigation';

const OrderHeader = ({ orders }: { orders: OrderResponse | null }) => {
  const router = useRouter();
  const params = useSearchParams();

  const { register, handleSubmit, resetField } = useForm<OrderFilteredPayload>({
    defaultValues: {
      page: parseInt(params.get('page') || '0', 10),
      size: parseInt(params.get('size') || '10', 10),
      customerName: params.get('customerName') || '',
      customerMail: params.get('customerMail') || '',
      orderCode: params.get('orderCode') || '',
      reservationCode: params.get('reservationCode') || '',
      hotelCode: params.get('hotelCode') || '',
      payerName: params.get('payerName') || '',
      payerMail: params.get('payerMail') || '',
      guestName: params.get('guestName') || '',
      reference: params.get('reference') || '',
      sentDateStart: params.get('sentDateStart') || '',
      sentDateEnd: params.get('sentDateEnd') || ''
    }
  });

  const handleFilter = (filters: OrderFilteredPayload) => {
    Object.keys(filters).forEach(key => {
      if (
        filters[key as keyof OrderFilteredPayload] === '' ||
        filters[key as keyof OrderFilteredPayload] === null
      ) {
        delete filters[key as keyof OrderFilteredPayload];
      }
    });

    const orderedKeys = Object.keys(defaultOrderFilteredPayload) as Array<
      keyof OrderFilteredPayload
    >;

    const params = new URLSearchParams();
    orderedKeys.forEach(key => {
      const val = filters[key];
      if (val !== null && val !== '' && val !== undefined) {
        params.set(key, String(val));
      }
    });

    router.push(`?${params.toString()}`);
  };

  const handleRemoveFilter = (key: string) => {
    const newParams = new URLSearchParams();
    for (const [k, v] of params.entries()) {
      if (k !== key) {
        newParams.set(k, v);
      }
    }

    resetField(key as keyof OrderFilteredPayload, { defaultValue: '' });
    router.push(`?${newParams.toString()}`);
  };

  const handleClearFilters = () => {
    const newParams = new URLSearchParams();
    newParams.set('page', params.get('page') || '0');
    newParams.set('size', params.get('size') || '10');

    const keys = Object.keys(defaultOrderFilteredPayload).filter(
      key => key !== 'page' && key !== 'size'
    ) as Array<keyof OrderFilteredPayload>;
    keys.forEach(key => {
      resetField(key, { defaultValue: '' });
    });

    router.push(`?${newParams.toString()}`);
  };

  return (
    <div className='flex items-center justify-between'>
      <div className='flex items-end gap-2'>
        <Sheet>
          <SheetTrigger asChild>
            <Button color='white' size='small'>
              <Sliders />
              Filtrar
            </Button>
          </SheetTrigger>
          <SheetContent>
            <form onSubmit={handleSubmit(handleFilter)}>
              <SheetHeader>
                <SheetTitle>Filtros</SheetTitle>
              </SheetHeader>
              <div className='flex flex-col gap-2 mt-2'>
                <Input
                  {...register('customerName')}
                  color='gray'
                  placeholder='Nome do cliente'
                />
                <Input
                  {...register('customerMail')}
                  color='gray'
                  placeholder='E-mail do cliente'
                />
                <div className='flex gap-2'>
                  <Input
                    {...register('orderCode')}
                    color='gray'
                    placeholder='Código do pedido (OT)'
                  />
                  <Input
                    {...register('reservationCode')}
                    color='gray'
                    placeholder='Código da reserva (OTB)'
                  />
                </div>
                <Input
                  {...register('hotelCode')}
                  color='gray'
                  placeholder='Código do hotel'
                />
                <Input
                  {...register('payerName')}
                  color='gray'
                  placeholder='Nome do pagador'
                />
                <Input
                  {...register('payerMail')}
                  color='gray'
                  placeholder='E-mail do pagador'
                />
                <Input
                  {...register('guestName')}
                  color='gray'
                  placeholder='Nome do hóspede'
                />
                <Input
                  {...register('reference')}
                  color='gray'
                  placeholder='Número de confirmação'
                />
                <div className='flex gap-2'>
                  <Input
                    {...register('sentDateStart')}
                    placeholder='Início da reserva'
                    type='date'
                    color='gray'
                  />
                  <Input
                    type='date'
                    {...register('sentDateEnd')}
                    color='gray'
                    placeholder='Fim da reserva'
                  />
                </div>
                <SheetFooter>
                  <SheetClose asChild>
                    <div className='w-full flex gap-2 mt-2'>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleClearFilters}
                      >
                        Limpar
                      </Button>
                      <Button type='submit' color='primary' className='w-full'>
                        Filtrar
                      </Button>
                    </div>
                  </SheetClose>
                </SheetFooter>
              </div>
            </form>
          </SheetContent>
        </Sheet>
        <div className='flex flex-wrap gap-2'>
          {Array.from(params.entries())
            .filter(([key]) => key !== 'page' && key !== 'size')
            .map(([key, value]) => (
              <div
                key={key}
                className='bg-white rounded-inner flex items-center gap-2 px-3 py-1.5'
              >
                <X
                  size={14}
                  className='text-gray-500 cursor-pointer hover:scale-125 transition-transform'
                  onClick={() => handleRemoveFilter(key)}
                />
                <p className='text-sm'>{value}</p>
              </div>
            ))}
        </div>
      </div>
      <p className='text-sm text-gray-500'>
        Exibindo {orders?.numberOfElements} de {orders?.totalElements} pedidos
      </p>
    </div>
  );
};

export default OrderHeader;
