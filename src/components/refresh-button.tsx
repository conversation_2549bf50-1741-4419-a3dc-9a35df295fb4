'use client';

import { ArrowClockwise } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';

const RefreshButton = () => {
  const router = useRouter();

  const handleRefresh = () => {
    router.refresh();
  };

  return (
    <div
      onClick={handleRefresh}
      className='text-sm text-gray-500 flex gap-1 items-center mb-[2px] cursor-pointer hover:text-gray-700'
    >
      <ArrowClockwise />
      <p>Atualizar</p>
    </div>
  );
};

export default RefreshButton;
