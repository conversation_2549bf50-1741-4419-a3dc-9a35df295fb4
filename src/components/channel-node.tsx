import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { memo } from 'react';

const ChannelNode = memo(
  ({ data }: { data: { label: string; code: string } }) => {
    return (
      <>
        <Handle type='target' position={Position.Right} />
        <div className='flex flex-col bg-white rounded-default px-3 py-2'>
          <h3 className='text-primary-900 font-medium'>{data.label}</h3>
          <p className='text-gray-500 text-sm'>{data.code}</p>
        </div>
      </>
    );
  }
);

export default ChannelNode;
