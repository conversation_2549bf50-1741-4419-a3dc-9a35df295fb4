'use client';

import { trivagoServiceRestart, trivagoServiceStart } from '@/services/timi';
import {
  ArrowClockwise,
  CircleNotch,
  Dot,
  Play,
  TerminalWindow
} from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

const TimiStatus = ({
  trivagoStatus,
  timiStatus,
  intelligenceStatus
}: {
  trivagoStatus: boolean;
  timiStatus: boolean;
  intelligenceStatus: boolean;
}) => {
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  const handleTrivagoRefresh = async () => {
    setIsRefreshing(true);

    try {
      const response = await trivagoServiceRestart();
      if (response.data) {
        toast.success('Trivago Service reiniciado com sucesso');
        router.refresh();
      } else {
        toast.error('Falha ao reiniciar Trivago Service');
      }
    } catch (error) {
      toast.error('Falha ao reiniciar Trivago Service');
    }

    setIsRefreshing(false);
  };

  const handleTrivagoStart = async () => {
    setIsRefreshing(true);

    try {
      const response = await trivagoServiceStart();
      if (response.data) {
        toast.success('Trivago Service iniciado com sucesso');
        router.refresh();
      } else {
        toast.error('Falha ao iniciar Trivago Service');
      }
    } catch (error) {
      toast.error('Falha ao iniciar Trivago Service');
    }

    setIsRefreshing(false);
  };

  const handleTrivagoAction = async () => {
    if (isRefreshing) return;

    if (trivagoStatus) {
      handleTrivagoRefresh();
    }

    if (!trivagoStatus) {
      handleTrivagoStart();
    }
  };

  return (
    <div className='w-full flex flex-col gap-2 bg-white rounded-default p-3'>
      <p className='text-sm text-gray-500'>Status</p>
      <div
        className={`flex items-center justify-between ${trivagoStatus ? 'bg-success-100/50' : 'bg-danger-100/50'} px-4 py-2 rounded-inner`}
      >
        <div className='flex items-center'>
          <span className='relative flex size-3'>
            <span
              className={`absolute inline-flex h-full w-full animate-ping rounded-full ${trivagoStatus ? 'bg-success-500' : 'bg-red-500'} opacity-75`}
            ></span>
            <span
              className={`relative inline-flex size-3 rounded-full ${trivagoStatus ? 'bg-success-500' : 'bg-red-500'}`}
            ></span>
          </span>
          <div className='flex flex-col gap-0.5 ml-4'>
            <p className='text-gray-900 font-medium text-sm leading-4'>
              Trivago Service
            </p>
            <p
              className={`flex items-center gap-1.5 text-xs ${trivagoStatus ? 'text-success-800' : 'text-red-800'} leading-3`}
            >
              {trivagoStatus ? 'Online' : 'Offline'}
              <span className='w-1 h-1 bg-success-500 rounded-full' />
              <span
                className='text-xs text-gray-500 cursor-pointer'
                onClick={() => router.push('/timi/logs')}
              >
                Ver logs
              </span>
            </p>
          </div>
        </div>
        <div className='flex items-center gap-3 pr-1'>
          <div onClick={handleTrivagoAction}>
            {isRefreshing ? (
              <CircleNotch className='animate-spin' weight='bold' />
            ) : trivagoStatus ? (
              <ArrowClockwise size={18} weight='bold' />
            ) : (
              <Play size={18} weight='bold' />
            )}
          </div>
        </div>
      </div>
      <div
        className={`flex items-center ${intelligenceStatus ? 'bg-success-100/50' : 'bg-danger-100/50'} px-4 py-2 rounded-inner`}
      >
        <span className='relative flex size-3'>
          <span
            className={`absolute inline-flex h-full w-full animate-ping rounded-full ${intelligenceStatus ? 'bg-success-500' : 'bg-red-500'} opacity-75`}
          ></span>
          <span
            className={`relative inline-flex size-3 rounded-full ${intelligenceStatus ? 'bg-success-500' : 'bg-red-500'}`}
          ></span>
        </span>
        <div className='flex flex-col gap-0.5 ml-4'>
          <p className='text-gray-900 font-medium text-sm leading-4'>
            Intelligence Service
          </p>
          <p
            className={`text-xs ${intelligenceStatus ? 'text-success-800' : 'text-red-800'} leading-3`}
          >
            {intelligenceStatus ? 'Online' : 'Offline'}
          </p>
        </div>
      </div>
      <div
        className={`flex items-center ${timiStatus ? 'bg-success-100/50' : 'bg-danger-100/50'} px-4 py-2 rounded-inner`}
      >
        <span className='relative flex size-3'>
          <span
            className={`absolute inline-flex h-full w-full animate-ping rounded-full ${timiStatus ? 'bg-success-500' : 'bg-red-500'} opacity-75`}
          ></span>
          <span
            className={`relative inline-flex size-3 rounded-full ${timiStatus ? 'bg-success-500' : 'bg-red-500'}`}
          ></span>
        </span>
        <div className='flex flex-col gap-0.5 ml-4'>
          <p className='text-gray-900 font-medium text-sm leading-4'>
            Timi Service
          </p>
          <p
            className={`text-xs ${timiStatus ? 'text-success-800' : 'text-red-800'} leading-3`}
          >
            {timiStatus ? 'Online' : 'Offline'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default TimiStatus;
