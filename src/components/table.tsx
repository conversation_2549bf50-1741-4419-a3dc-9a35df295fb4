'use client';

import { ReactNode } from 'react';

export type TableColumn<T, K extends keyof T> = {
  id: K;
  name: string;
  flex?: number;
  width?: number;
  align?: string;
};

export type TableRowData<T> = T & {
  separator?: ReactNode;
};

type ITableHeader<T, K extends keyof T> = {
  columns: Array<TableColumn<T, K>>;
};

interface ITable<T, K extends keyof T> {
  columns: Array<TableColumn<T, K>>;
  items: Array<TableRowData<T>>;
  loading?: boolean;
  footer?: ReactNode;
  header?: boolean;
}

const TableHeader = <T, K extends keyof T>({ columns }: ITableHeader<T, K>) => {
  const headers = columns.map((column, index) => (
    <th
      key={`th-${index}`}
      className={`w-[${column.width}px] px-6 py-3 text-sm font-medium text-gray-500 text-${column.align || 'left'} last:text-right`}
    >
      {column.name}
    </th>
  ));

  return (
    <thead className='border-b-2 border-gray-100'>
      <tr>{headers}</tr>
    </thead>
  );
};

type TableRowsProps<T, K extends keyof T> = {
  items: Array<TableRowData<T>>;
  columns: Array<TableColumn<T, K>>;
  loading: boolean;
};

const TableRows = <T, K extends keyof T>({
  items,
  columns,
  loading
}: TableRowsProps<T, K>) => {
  if (loading) {
    return (
      <tbody>
        {Array.from({ length: 5 }).map((_, index) => (
          <tr key={`loading-row-${index}`} className='h-12'>
            {columns.map((_, i) => (
              <td
                key={`loading-cell-${i}`}
                className='px-6 py-2 whitespace-nowrap text-left last:text-right'
              >
                <div
                  style={{
                    width: `${Math.floor(Math.random() * (100 - 50 + 1)) + 50}%`
                  }}
                  className='animate-pulse bg-gray-200 h-4 rounded-inner'
                />
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    );
  }

  if (items.length === 0) {
    return (
      <tbody>
        <tr>
          <td
            colSpan={columns.length}
            className={`px-6 py-6 whitespace-nowrap text-left last:text-right`}
          >
            <p className='text-gray-500 text-sm text-center'>
              Nenhum dado encontrado!
            </p>
          </td>
        </tr>
      </tbody>
    );
  }

  const rows = items.flatMap((row, index) => {
    const dataRow = (
      <tr
        key={`row-${index}`}
        className='w-full hover:bg-gray-100 cursor-default'
      >
        {columns.map((column, i) => (
          <td
            key={`cell-${i}`}
            className={`w-[${column.width}px] px-6 py-2 whitespace-nowrap text-${column.align || 'left'} last:text-right`}
          >
            {row[column.id] as ReactNode}
          </td>
        ))}
      </tr>
    );

    if (row.separator) {
      const separatorRow = (
        <tr key={`separator-${index}`} className='h-1'>
          <td colSpan={columns.length} className='px-3 py-2 pt-4'>
            {row.separator}
          </td>
        </tr>
      );
      return [dataRow, separatorRow];
    }

    return [dataRow];
  });

  return <tbody>{rows}</tbody>;
};

const Table = <T, K extends keyof T>({
  columns,
  items,
  loading = false,
  header = true
}: ITable<T, K>) => {
  return (
    <>
      <table className='bg-white w-full rounded-default'>
        {header && <TableHeader columns={columns} />}
        <TableRows items={items} columns={columns} loading={loading} />
      </table>
    </>
  );
};

export default Table;
