'use client';

import { Button } from '@ourtrip/ui';
import {
  ChartLineUp,
  CurrencyCircleDollar,
  GitBranch,
  IdentificationCard,
  Note,
  Pause,
  Robot,
  ShareNetwork,
  SidebarSimple,
  Tag,
  Ticket,
  Users
} from '@phosphor-icons/react';
import Image from 'next/image';
import SidebarItem, { SidebarItemProps } from './sidebar-item';
import SidebarUser from './sidebar-user';

export const Sidebar = ({
  user
}: {
  user: {
    id: string;
    name: string;
    email: string;
    profiles: string[];
    privileges: string[];
  };
}) => {
  const menuItems: SidebarItemProps[] = [
    { href: '/dashboard', icon: ChartLineUp, label: 'Dashboard' },
    { href: '/orders', icon: Ticket, label: 'Pedidos' },
    {
      icon: Users,
      label: 'Usuários',
      items: [
        { href: '/users/customers', icon: Users, label: 'Clientes' },
        { href: '/users/admins', icon: Users, label: 'Administradores' },
        { href: '/users/profiles', icon: Users, label: 'Perfis' }
      ]
    },
    { href: '/tripcash', icon: Tag, label: 'Tripcash' },
    {
      icon: GitBranch,
      label: 'Fornecedores',
      items: [
        {
          href: '/suppliers/providers',
          icon: ShareNetwork,
          label: 'Provedores'
        },
        { href: '/suppliers/channels', icon: Pause, label: 'Canais' },
        {
          href: '/suppliers/providers-accounts',
          icon: IdentificationCard,
          label: 'Contas de Provedores'
        },
        { href: '/suppliers/rates', icon: CurrencyCircleDollar, label: 'Taxas' }
      ]
    },
    {
      icon: Note,
      label: 'Notas Fiscais',
      items: [
        { href: '/invoices/send', icon: Note, label: 'Enviar NFe' },
        { href: '/invoices', icon: Note, label: 'Consulta' }
      ]
    }
  ];

  if (user?.privileges?.includes('CAN_MANAGE_RATE')) {
    menuItems.push({
      icon: Robot,
      label: 'Timi',
      href: '/timi',
      tag: 'Beta'
    });
  }

  return (
    <div className='w-72 flex flex-none flex-col bg-white p-6 rounded-default justify-between select-none'>
      <div className='flex flex-col gap-4 overflow-hidden'>
        <div className='w-full flex justify-between items-center pl-3'>
          <Image
            src='/horizontal_icon_text_blue.svg'
            alt='OurTrip'
            width={96}
            height={26}
          />
          <Button color='white' size='icon'>
            <SidebarSimple size={18} />
          </Button>
        </div>
        <nav className='overflow-auto scrollbar'>
          <ul className='flex flex-col'>
            {menuItems.map(
              item =>
                item && (
                  <SidebarItem
                    key={item?.label}
                    href={item?.href}
                    icon={item?.icon}
                    label={item?.label || ''}
                    items={item?.items}
                    tag={item?.tag}
                  />
                )
            )}
          </ul>
        </nav>
      </div>
      <SidebarUser user={user} />
    </div>
  );
};

export default Sidebar;
