'use client';

import { convertNumberToCurrency } from '@/lib/utils/currency';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@ourtrip/ui';
import { Info } from '@phosphor-icons/react';

const OrderValue = ({
  value,
  currency,
  currencySymbol,
  installments,
  installmentAmount,
  method
}: {
  value: string;
  currency: string;
  currencySymbol: string;
  installments?: number;
  installmentAmount?: number;
  method: string;
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <div
            className={`flex items-center  gap-1 text-primary-900 text-sm font-medium ${method === 'CREDIT_CARD' ? 'cursor-pointer' : ''}`}
          >
            {method === 'CREDIT_CARD' && <Info />} {currencySymbol} {value}
          </div>
        </TooltipTrigger>
        <TooltipContent className='bg-white' side='bottom'>
          <p className='text-sm text-primary-900'>
            {method === 'CREDIT_CARD'
              ? `${installments}x de ${currencySymbol} ${convertNumberToCurrency(
                  currency!,
                  installmentAmount!
                )}`
              : 'Á vista'}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default OrderValue;
