'use client';

import { Provider } from '@/lib/definitions/provider';
import {
  Background,
  BackgroundVariant,
  ReactFlow,
  Node,
  useNodesState,
  useEdgesState,
  Edge,
  Panel,
  useReactFlow,
  ReactFlowInstance
} from '@xyflow/react';

import '@xyflow/react/dist/style.css';
import ProviderNode from './provider-node';
import { Channel } from '@/lib/definitions/channel';
import ChannelNode from './channel-node';
import { ProviderAccount } from '@/lib/definitions/provider-account';
import ProviderAccountNode from './provider-account-node';
import { Rate } from '@/lib/definitions/rate';
import RateNode from './rate-node';
import { useState } from 'react';
import { Button } from '@ourtrip/ui';
import { ArrowCounterClockwise, FloppyDiskBack } from '@phosphor-icons/react';
import { position } from 'html2canvas-pro/dist/types/css/property-descriptors/position';

const nodeTypes = {
  channel: ChannelNode,
  provider: ProviderNode,
  providerAccount: ProviderAccountNode,
  rate: RateNode
};

const SupplierPanel = ({
  channels,
  providers,
  providersAccounts,
  rates
}: {
  channels: Channel[];
  providers: Provider[];
  providersAccounts: ProviderAccount[];
  rates: Rate[];
}) => {
  const { setViewport } = useReactFlow();
  const [rfInstance, setRfInstance] = useState<ReactFlowInstance<Node, Edge>>();

  const providersNodes: Node[] = providers
    .filter((provider): provider is Provider & { id: string } => !!provider.id)
    .map((provider, index) => ({
      id: provider.code,
      type: 'provider',
      position: { x: 0, y: index * 125 },
      data: {
        label: provider.name,
        code: provider.code,
        enabled: provider.enabled
      }
    }));

  const channelsNodes: Node[] = channels
    .filter((channel): channel is Channel & { id: string } => !!channel.id)
    .map((channel, index) => ({
      id: channel.id,
      type: 'channel',
      position: { x: -1000, y: index * 80 },
      data: {
        label: channel.name,
        code: channel.code,
        enabled: channel.enabled
      }
    }));

  const providersAccountsNodes: Node[] = providersAccounts
    .filter(
      (providerAccount): providerAccount is ProviderAccount & { id: string } =>
        !!providerAccount.id
    )
    .map((providerAccount, index) => ({
      id: providerAccount.code,
      type: 'providerAccount',
      position: { x: -400, y: index * 125 },
      data: {
        code: providerAccount.code,
        provider: providerAccount.providerId,
        enabled: providerAccount.enabled
      }
    }));

  const ratesNodes: Node[] = rates
    .filter((rate): rate is Rate & { id: string } => !!rate.id)
    .map((rate, index) => ({
      id: rate.id,
      type: 'rate',
      position: { x: -700, y: index * 125 },
      data: {
        id: rate.id,
        handleChannelId: rate.id + '-channel',
        channel: rate.channel,
        markup: rate.markup,
        tax: rate.tax
      }
    }));

  const providersAccountsEdges: Edge[] = providersAccounts
    .filter(
      (
        providerAccount
      ): providerAccount is ProviderAccount & {
        id: string;
        providerId: string;
      } =>
        !!providerAccount.id &&
        !!providerAccount.providerId &&
        Boolean(providers.find(p => p.id === providerAccount.providerId))
    )
    .map(providerAccount => {
      if (providers.find(p => p.id === providerAccount.providerId)) {
        return Object({
          id: providerAccount.id,
          source: providerAccount.code,
          target: providers.find(p => p.id === providerAccount.providerId)!
            .code,
          animated: true
        });
      }
      return null;
    })
    .filter(edge => edge !== null) as Edge[];

  const ratesEdges: Edge[] = rates
    .filter(
      (rate): rate is Rate & { id: string; providerAccountId: string } =>
        !!rate.id &&
        !!rate.provider &&
        Boolean(providersAccounts.find(p => p.code === rate.providerAccount))
    )
    .map(rate => {
      if (providersAccounts.find(p => p.code === rate.providerAccount)) {
        return Object({
          id: rate.id + '-providerAccount',
          source: rate.id,
          target: rate.providerAccount,
          animated: true
        });
      }
      return null;
    })
    .filter(edge => edge !== null) as Edge[];

  const ratesChannelEdges: Edge[] = rates
    .filter(
      (rate): rate is Rate & { id: string } =>
        !!rate.id && Boolean(channels.find(c => c.code === rate.channel))
    )
    .map(rate => {
      return Object({
        id: rate.id + '-channel',
        source: rate.id,
        target: channels.find(c => c.code === rate.channel)!.id,
        sourceHandle: rate.id + '-channel',
        animated: true
      });
    });

  const ratesProviderEdges: Edge[] = rates
    .filter(
      (rate): rate is Rate & { id: string } =>
        !!rate.id && Boolean(providers.find(p => p.code === rate.provider))
    )
    .map(rate => {
      return Object({
        id: rate.id + '-provider',
        source: rate.id,
        target: rate.provider,
        animated: true
      });
    });

  const [nodes, setNodes, onNodesChange] = useNodesState([
    ...channelsNodes,
    ...providersNodes,
    ...providersAccountsNodes,
    ...ratesNodes
  ]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([
    ...providersAccountsEdges,
    ...ratesProviderEdges,
    ...ratesChannelEdges,
    ...ratesEdges
  ]);

  const onSave = () => {
    if (rfInstance) {
      const flow = rfInstance.toObject();
      localStorage.setItem('panel', JSON.stringify(flow));
    }
  };

  const onRestore = () => {
    const flow = JSON.parse(localStorage.getItem('panel')!);

    if (flow) {
      const { x = 0, y = 0, zoom = 1 } = flow.viewport;

      const storedNodes = flow.nodes.map((node: Node) => {
        return { ...node, position: { x, y } };
      });

      const allNodes = [
        ...channelsNodes,
        ...providersNodes,
        ...providersAccountsNodes,
        ...ratesNodes,
        ...storedNodes
      ];

      setNodes(allNodes || []);
      setEdges(flow.edges || []);
      setViewport({ x, y, zoom });
    }
  };

  return (
    <div className='h-full w-full'>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onInit={setRfInstance}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
      >
        <Panel position='top-right' className='flex gap-2'>
          <Button onClick={onSave} size='icon' color='white'>
            <FloppyDiskBack size={18} />
          </Button>
          <Button onClick={onRestore} size='icon' color='white'>
            <ArrowCounterClockwise size={18} />
          </Button>
        </Panel>
        <Background
          variant={BackgroundVariant.Lines}
          gap={22}
          size={100}
          bgColor='#F9F9F9'
          className='rounded-default'
          color='#F1F1F1'
        />
      </ReactFlow>
    </div>
  );
};

export default SupplierPanel;
