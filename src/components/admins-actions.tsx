'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from '@ourtrip/ui';
import {
  Circle<PERSON>ot<PERSON>,
  DotsThree,
  Pause,
  <PERSON>ci<PERSON>,
  Play
} from '@phosphor-icons/react';
import AdminSheet from './admin-sheet';
import { useState } from 'react';
import { Admin } from '@/lib/definitions/admin';
import { updateAdmin } from '@/services/admin';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const AdminsActions = ({ admin }: { admin: Admin }) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isToggling, setIsToggling] = useState<boolean>(false);

  const handleUpdateAdmin = async (admin: Admin) => {
    setIsEditing(true);

    const response = await updateAdmin(admin);
    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Administrador atualizado com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };

  const handleToggleActive = async () => {
    if (!admin) return;

    setIsToggling(true);

    const response = await updateAdmin({
      ...admin,
      enabled: !admin.enabled
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Administrador atualizado com sucesso');
      router.refresh();
    }

    setIsToggling(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <AdminSheet
            admin={admin}
            submit={handleUpdateAdmin}
            loading={isEditing}
          >
            <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
              <Pencil />
              <p className='text-sm text-primary-900 font-medium'>Editar</p>
            </div>
          </AdminSheet>
          <div
            className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
            onClick={handleToggleActive}
          >
            {isToggling ? (
              <CircleNotch className='animate-spin' weight='bold' />
            ) : !admin.enabled ? (
              <Play />
            ) : (
              <Pause />
            )}
            <p className='text-sm text-primary-900 font-medium'>
              {!admin.enabled ? 'Habilitar' : 'Desabilitar'}
            </p>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default AdminsActions;
