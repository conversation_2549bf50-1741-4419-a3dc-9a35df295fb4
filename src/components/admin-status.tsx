'use client';

import { Admin } from '@/lib/definitions/admin';
import { Badge } from '@ourtrip/ui';
import { Check, X } from '@phosphor-icons/react';

const AdminStatus = ({ admin }: { admin: Admin }) => {
  return (
    <Badge
      className='flex w-min'
      type={admin.enabled ? 'success' : 'danger'}
      icon={admin.enabled ? <Check /> : <X />}
      size='small'
    >
      {admin.enabled ? 'Ativo' : 'Inativo'}
    </Badge>
  );
};

export default AdminStatus;
