'use client';

import { TripcashPayload, TripcashResponse } from '@/lib/definitions/tripcash';
import { updateTripcash } from '@/services/tripcash';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Pause,
  Pencil,
  Play
} from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import TripcashSheet from './tripcash-sheet';
import { useState } from 'react';
import { toast } from 'sonner';

const TripcashActions = ({ tripcash }: { tripcash: TripcashResponse }) => {
  const router = useRouter();
  const [isToggling, setIsToggling] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const handleUpdateTripcash = async (tripcash: TripcashPayload) => {
    setIsEditing(true);
    const response = await updateTripcash(tripcash);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Tripcash atualizado com sucesso');
      router.refresh();
    }
    setIsEditing(false);
  };

  const handleToggleActive = async () => {
    setIsToggling(true);
    const response = await updateTripcash({
      ...tripcash,
      active: !tripcash.active
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Tripcash atualizado com sucesso');
      router.refresh();
    }

    setIsToggling(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col gap-1'>
            <TripcashSheet
              tripcash={tripcash}
              submit={handleUpdateTripcash}
              loading={isEditing}
            >
              <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
                <Pencil />
                <p className='text-sm text-primary-900 font-medium'>Editar</p>
              </div>
            </TripcashSheet>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={handleToggleActive}
            >
              {isToggling ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : !tripcash.active ? (
                <Play />
              ) : (
                <Pause />
              )}
              <p className='text-sm text-primary-900 font-medium'>
                {!tripcash.active ? 'Ativar' : 'Desativar'}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default TripcashActions;
