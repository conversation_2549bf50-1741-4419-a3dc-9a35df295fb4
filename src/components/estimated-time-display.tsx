'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import dayjs from 'dayjs';

interface EstimatedTimeDisplayProps {
  startTime: string;
  processedCount: number;
  totalCount: number;
  className?: string;
}

const EstimatedTimeDisplay = ({
  startTime,
  processedCount,
  totalCount,
  className = ''
}: EstimatedTimeDisplayProps) => {
  const [duration, setDuration] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setDuration(dayjs().diff(dayjs(startTime), 'second'));
  }, [startTime]);

  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      setDuration(dayjs().diff(dayjs(startTime), 'second'));
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [startTime]);

  const estimatedTimeLeft = useMemo(() => {
    if (processedCount === 0) return 0;
    const averageTimePerHotel = duration / processedCount;
    const hotelsLeft = totalCount - processedCount;
    return Math.round(averageTimePerHotel * hotelsLeft);
  }, [duration, processedCount, totalCount]);

  const averageTimePerHotel = useMemo(() => {
    return duration / (processedCount || 1);
  }, [duration, processedCount]);

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedSeconds = seconds.toString().padStart(2, '0');

    return hours > 0
      ? `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
      : `${formattedMinutes}:${formattedSeconds}`;
  };

  return (
    <>
      <div>
        <p className='text-sm text-gray-500'>Média:</p>
        <h3 className='text-primary-900 font-medium'>
          {averageTimePerHotel.toFixed(2)}s/hotel
        </h3>
      </div>
      <div>
        <p className='text-sm text-gray-500'>Estimado:</p>
        <h3 className={`text-primary-900 font-medium ${className}`}>
          {formatDuration(estimatedTimeLeft)}
        </h3>
      </div>
    </>
  );
};

export default EstimatedTimeDisplay;
