'use client';

import * as React from 'react';
import { CaretDown, CaretLeft, CaretRight } from '@phosphor-icons/react';
import { DayButton, DayPicker, getDefaultClassNames } from 'react-day-picker';
import { Button } from '@/components/button';
import { capitalizeFirstLetter } from '@/lib/utils/strings';
import { ptBR } from 'react-day-picker/locale';
import { cn } from '@/lib/utils/classes';

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  captionLayout = 'label',
  formatters,
  components,
  ...props
}: React.ComponentProps<typeof DayPicker> & {
  buttonVariant?: React.ComponentProps<typeof Button>['variant'];
}) {
  const defaultClassNames = getDefaultClassNames();

  return (
    <DayPicker
      locale={ptBR}
      showOutsideDays={showOutsideDays}
      className={cn(
        'bg-white group/calendar p-4 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-white [[data-slot=popover-content]_&]:bg-white',
        String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,
        String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,
        className
      )}
      captionLayout={captionLayout}
      formatters={{
        formatMonthDropdown: date =>
          date.toLocaleString('pt-BR', { month: 'short' }),
        ...formatters
      }}
      classNames={{
        root: cn('w-fit', defaultClassNames.root),
        months: cn(
          'flex gap-4 flex-col md:flex-row relative',
          defaultClassNames.months
        ),
        month: cn('flex flex-col w-full gap-4', defaultClassNames.month),
        nav: cn(
          'flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between',
          defaultClassNames.nav
        ),
        button_previous: cn(
          'size-(--cell-size) aria-disabled:opacity-50 p-0 select-none',
          defaultClassNames.button_previous
        ),
        button_next: cn(
          'size-(--cell-size) aria-disabled:opacity-50 p-0 select-none',
          defaultClassNames.button_next
        ),
        month_caption: cn(
          'flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)',
          defaultClassNames.month_caption
        ),
        dropdowns: cn(
          'w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5',
          defaultClassNames.dropdowns
        ),
        dropdown_root: cn(
          'relative shadow-xs rounded-default',
          defaultClassNames.dropdown_root
        ),
        dropdown: cn('absolute inset-0 opacity-0', defaultClassNames.dropdown),
        caption_label: cn(
          'select-none font-medium',
          captionLayout === 'label'
            ? 'text-sm'
            : 'rounded-inner pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-primary-900 [&>svg]:size-3.5',
          defaultClassNames.caption_label
        ),
        table: 'w-full border-collapse',
        weekdays: cn('flex', defaultClassNames.weekdays),
        weekday: cn(
          'text-primary-900 rounded-inner flex-1 font-normal text-[0.8rem] select-none',
          defaultClassNames.weekday
        ),
        week: cn('flex w-full mt-2 gap-1', defaultClassNames.week),
        week_number_header: cn(
          'select-none w-(--cell-size)',
          defaultClassNames.week_number_header
        ),
        week_number: cn(
          'text-[0.8rem] select-none text-primary-900',
          defaultClassNames.week_number
        ),
        day: cn(
          'relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-inner [&:last-child[data-selected=true]_button]:rounded-r-inner group/day aspect-square select-none',
          defaultClassNames.day
        ),
        range_start: cn(
          'rounded-l-inner bg-primary-500 text-white',
          defaultClassNames.range_start
        ),
        range_middle: cn('rounded-none', defaultClassNames.range_middle),
        range_end: cn(
          'rounded-r-inner bg-primary-500 text-white',
          defaultClassNames.range_end
        ),
        today: cn(
          'bg-primary-100 text-primary-500 rounded-inner data-[selected=true]:rounded-none',
          defaultClassNames.today
        ),
        outside: cn(
          'text-primary-900 aria-selected:text-primary-900',
          defaultClassNames.outside
        ),
        disabled: cn('text-primary-900 opacity-50', defaultClassNames.disabled),
        hidden: cn('invisible', defaultClassNames.hidden),
        ...classNames
      }}
      components={{
        Root: ({ className, rootRef, ...props }) => {
          return (
            <div
              data-slot='calendar'
              ref={rootRef}
              className={cn('w-full', className)}
              {...props}
            />
          );
        },
        CaptionLabel: ({ children }) => {
          return (
            <div className='flex items-center gap-1'>
              <p className='text-sm font-medium text-primary-900'>
                {capitalizeFirstLetter(children as string)}
              </p>
            </div>
          );
        },
        Weekday: ({ children, ...props }) => {
          return (
            <th {...props}>
              <div className='flex items-center justify-center text-gray-500'>
                {capitalizeFirstLetter(children as string)}
              </div>
            </th>
          );
        },
        Chevron: ({ className, orientation, ...props }) => {
          if (orientation === 'left') {
            return <CaretLeft className={cn('size-4', className)} {...props} />;
          }

          if (orientation === 'right') {
            return (
              <CaretRight className={cn('size-4', className)} {...props} />
            );
          }

          return <CaretDown className={cn('size-4', className)} {...props} />;
        },
        DayButton: CalendarDayButton,
        WeekNumber: ({ children, ...props }) => {
          return (
            <td {...props}>
              <div className='flex size-(--cell-size) items-center justify-center text-center'>
                {children}
              </div>
            </td>
          );
        },
        ...components
      }}
      {...props}
    />
  );
}

function CalendarDayButton({
  className,
  day,
  modifiers,
  ...props
}: React.ComponentProps<typeof DayButton>) {
  const defaultClassNames = getDefaultClassNames();

  const ref = React.useRef<HTMLButtonElement>(null);
  React.useEffect(() => {
    if (modifiers.focused) ref.current?.focus();
  }, [modifiers.focused]);

  return (
    <Button
      ref={ref}
      variant='ghost'
      size='icon'
      data-day={day.date.toLocaleDateString()}
      data-selected-single={
        modifiers.selected &&
        !modifiers.range_start &&
        !modifiers.range_end &&
        !modifiers.range_middle
      }
      data-range-start={modifiers.range_start}
      data-range-end={modifiers.range_end}
      data-range-middle={modifiers.range_middle}
      className={cn(
        'data-[selected-single=true]:bg-primary-500 data-[selected-single=true]:text-white data-[range-middle=true]:bg-primary-100 data-[range-middle=true]:text-primary-900 data-[range-start=true]:bg-primary-500 data-[range-start=true]:text-white data-[range-end=true]:bg-primary-500 data-[range-end=true]:text-white group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-inner data-[range-end=true]:rounded-r-inner data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-inner data-[range-start=true]:rounded-l-inner [&>span]:text-xs [&>span]:opacity-70',
        defaultClassNames.day,
        className
      )}
      {...props}
    />
  );
}

export { Calendar, CalendarDayButton };
