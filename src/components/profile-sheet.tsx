'use client';

import { AdminPrivilege, AdminProfile } from '@/lib/definitions/admin';
import { getPrivileges } from '@/services/admin';
import {
  Button,
  Checkbox,
  Input,
  Sheet,
  SheetClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger
} from '@ourtrip/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const ProfileSheet = ({
  children,
  profile,
  submit,
  loading
}: {
  children: React.ReactNode;
  profile?: AdminProfile;
  submit: (data: AdminProfile) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [privileges, setPrivileges] = useState<AdminPrivilege[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    getValues,
    formState: { errors }
  } = useForm<AdminProfile>({
    defaultValues: {
      id: profile?.id || '',
      name: profile?.name || '',
      privileges: profile?.privileges || []
    }
  });

  const fetchPrivileges = async () => {
    const response = await getPrivileges();

    if (response.error) {
      toast.error(response.error);
    } else {
      setPrivileges(response.data!);
    }
  };

  const onSubmit = async (data: AdminProfile) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  useEffect(() => {
    fetchPrivileges();
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>
              {profile ? 'Editar perfil' : 'Criar perfil'}
            </SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-2 mt-2'>
            <Input
              {...register('name', { required: 'Campo obrigatório' })}
              error={errors?.name?.message}
              placeholder='Nome do perfil'
              color='gray'
            />
          </div>
          {privileges.length > 0 && (
            <div className='flex flex-col gap-1 mt-4'>
              {privileges.map(privilege => (
                <Checkbox
                  key={privilege.id}
                  value={getValues('privileges').some(
                    p => p.id === privilege.id
                  )}
                  onChange={checked => {
                    const updatedPrivileges = checked
                      ? [...(getValues('privileges') || []), privilege]
                      : (getValues('privileges') || []).filter(
                          p => p.id !== privilege.id
                        );

                    reset({
                      ...getValues(),
                      privileges: updatedPrivileges
                    });
                  }}
                  label={privilege.name}
                />
              ))}
            </div>
          )}
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {profile ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default ProfileSheet;
