'use client';

import * as React from 'react';
import { useState } from 'react';
import { Calendar } from '@/components/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/popover';

const DatePicker = ({
  children,
  onChange,
  ...props
}: React.ComponentProps<typeof Calendar> & {
  children: React.ReactNode;
  onChange: (date: Date) => void;
} & React.ComponentProps<typeof PopoverContent>) => {
  const [date, setDate] = useState<Date | undefined>(new Date());

  const handleDateChange = (newDate: Date | undefined) => {
    if (newDate) {
      setDate(newDate);
      onChange(newDate);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent {...props}>
        <Calendar
          {...props}
          mode='single'
          selected={date}
          onSelect={handleDateChange}
        />
      </PopoverContent>
    </Popover>
  );
};

export default DatePicker;
