'use client';

import { Provider } from '@/lib/definitions/provider';
import {
  Button,
  Input,
  Sheet,
  Sheet<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@ourtrip/ui';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const ProviderSheet = ({
  children,
  provider,
  submit,
  loading
}: {
  children: React.ReactNode;
  provider?: Provider;
  submit: (data: Provider) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<Provider>({
    defaultValues: {
      id: provider?.id || '',
      code: provider?.code || '',
      name: provider?.name || '',
      amountDestinations: provider?.amountDestinations || 0,
      amountHotels: provider?.amountHotels || 0,
      enabled: provider?.enabled || false
    }
  });

  const onSubmit = async (data: Provider) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>Adicionar Provedor</SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-4 mt-2'>
            <Input
              placeholder='Código *'
              {...register('code', { required: true })}
              error={errors.code?.message}
              color='gray'
            />
            <Input
              placeholder='Nome *'
              {...register('name', { required: true })}
              error={errors.name?.message}
              color='gray'
            />
            <div className='flex gap-2'>
              <Input
                label='Qtd. Hotéis *'
                placeholder='Qtd. Hotéis *'
                {...register('amountHotels', { required: true })}
                error={errors.amountHotels?.message}
                color='gray'
              />
              <Input
                label='Qtd. Destinos *'
                placeholder='Qtd. Destinos *'
                {...register('amountDestinations', { required: true })}
                error={errors.amountDestinations?.message}
                color='gray'
              />
            </div>
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {provider ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default ProviderSheet;
