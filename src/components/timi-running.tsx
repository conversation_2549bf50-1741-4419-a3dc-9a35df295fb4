'use client';

import { useEffect, useState, useMemo } from 'react';
import {
  Badge,
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@ourtrip/ui';
import {
  CircleNotch,
  Clock,
  DotsThree,
  StopCircle
} from '@phosphor-icons/react';
import supabase from '@/services/supabase-client';
import { Run } from '@/lib/definitions/timi';
import TimerDisplay from './timer-display';
import EstimatedTimeDisplay from './estimated-time-display';
import { useRouter } from 'next/navigation';
import { stopRun } from '@/services/timi';
import { toast } from 'sonner';

const TimiRunning = ({ runId }: { runId?: string }) => {
  const router = useRouter();
  const [run, setRun] = useState<Run | null>(null);
  const [isStopping, setIsStopping] = useState<boolean>(false);

  const calculateProgress = () => {
    if ((run?.hotels_count || 0) === 0) return 0;

    return (
      ((run?.processed_hotels_count || 0) / (run?.hotels_count || 0)) *
      100
    ).toFixed(0);
  };

  const handleStopRun = async () => {
    if (!runId) return;
    setIsStopping(true);

    const response = await stopRun(runId);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Execução parada com sucesso');
      router.refresh();
    }

    setIsStopping(false);
  };

  useEffect(() => {
    if (!runId) return;

    const fetchRun = async () => {
      const { data } = await supabase
        .from('run')
        .select('*')
        .eq('id', runId)
        .single();

      setRun(data!);
    };

    const realtimeRun = supabase
      .channel('schema-db-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'run',
          filter: `id=eq.${runId}`
        },
        payload => {
          setRun(payload.new as Run);
          if ((payload.new as Run).ended_at) {
            setRun(null);
            router.refresh();
          }
        }
      )
      .subscribe();

    fetchRun();

    return () => {
      supabase.removeChannel(realtimeRun);
    };
  }, [runId]);

  const progressPercentage = useMemo(
    () => calculateProgress(),
    [run?.hotels_count, run?.processed_hotels_count]
  );

  const parsedFilters = run?.filters
    ? (() => {
        try {
          const validJson = run.filters.replace(/'/g, '"');
          return JSON.parse(validJson);
        } catch (error) {
          console.error('Error parsing filters:', error);
          return [];
        }
      })()
    : [];

  return (
    run && (
      <div className='flex flex-col bg-white rounded-default p-3'>
        <div className='flex items-center justify-between gap-2'>
          <p className='text-sm font-medium flex items-center gap-1'>
            <Clock className='text-info-900' />
            Execução{' '}
            <span className='text-xs text-gray-500'>
              #{run.id.split('-')[0]}
            </span>
          </p>
          <div className='flex items-start gap-2'>
            <Badge type='info' size='small'>
              Executando
            </Badge>
            <Popover>
              <PopoverTrigger asChild>
                <div className='flex items-center justify-center gap-2 w-6 h-6 hover:bg-gray-200 rounded-inner cursor-pointer'>
                  <DotsThree size={18} weight='bold' />
                </div>
              </PopoverTrigger>
              <PopoverContent
                align='end'
                className='rounded-default shadow-2xl'
              >
                <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
                <div className='flex flex-col gap-1'>
                  <div
                    onClick={handleStopRun}
                    className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
                  >
                    {isStopping ? (
                      <CircleNotch className='animate-spin' weight='bold' />
                    ) : (
                      <StopCircle />
                    )}
                    <p className='text-sm text-primary-900 font-medium'>
                      Parar
                    </p>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className='mt-3'>
          <div className='flex gap-6'>
            <div>
              <p className='text-sm text-gray-500'>Tempo:</p>
              <TimerDisplay
                startTime={run.created_at}
                className='text-primary-900 font-medium'
              />
            </div>
            <EstimatedTimeDisplay
              startTime={run.created_at}
              processedCount={run.processed_hotels_count || 0}
              totalCount={run.hotels_count || 0}
            />
          </div>
          <div className='flex justify-between items-center mb-2 mt-2'>
            <p className='text-sm text-gray-500'>Progresso</p>
            <span className='text-sm font-medium text-primary-900'>
              {progressPercentage}%
            </span>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-info-600 h-2 rounded-full transition-all duration-300'
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className='flex justify-between items-center mt-1 text-sm text-gray-500'>
            <p>{run.processed_hotels_count}</p>
            <p>{run.hotels_count}</p>
          </div>
        </div>
        <div className='flex flex-col gap-2 mt-2'>
          {parsedFilters.map((filter: any, index: number) => (
            <div
              key={index}
              className='flex gap-1 items-center bg-gray-100 p-2 rounded-inner'
            >
              <p className='text-sm text-gray-500'>{filter.column}:</p>
              <p className='text-sm text-gray-500'>{filter.value}</p>
            </div>
          ))}
        </div>
      </div>
    )
  );
};

export default TimiRunning;
