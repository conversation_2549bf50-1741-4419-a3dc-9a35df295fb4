'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Pause,
  Pencil,
  Play
} from '@phosphor-icons/react';
import { ProviderAccount } from '@/lib/definitions/provider-account';
import { updateProviderAccount } from '@/services/providers-accounts';
import ProviderAccountSheet from './provider-account-sheet';
import { toast } from 'sonner';

const ProviderAccountActions = ({
  providerAccount
}: {
  providerAccount: ProviderAccount;
}) => {
  const router = useRouter();
  const [isToggling, setIsToggling] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const handleUpdateProviderAccount = async (
    providerAccount: ProviderAccount
  ) => {
    setIsEditing(true);
    const response = await updateProviderAccount(providerAccount);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Conta de provedor atualizada com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };

  const handleToggleActive = async () => {
    setIsToggling(true);
    const response = await updateProviderAccount({
      ...providerAccount,
      enabled: !providerAccount.enabled
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Conta de provedor atualizada com sucesso');
      router.refresh();
    }

    setIsToggling(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col gap-1'>
            <ProviderAccountSheet
              providerAccount={providerAccount}
              submit={handleUpdateProviderAccount}
              loading={isEditing}
            >
              <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
                <Pencil />
                <p className='text-sm text-primary-900 font-medium'>Editar</p>
              </div>
            </ProviderAccountSheet>
            <div
              onClick={handleToggleActive}
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
            >
              {isToggling ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : !providerAccount.enabled ? (
                <Play />
              ) : (
                <Pause />
              )}
              <p className='text-sm text-primary-900 font-medium'>
                {!providerAccount.enabled ? 'Habilitar' : 'Desabilitar'}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ProviderAccountActions;
