'use client';

import { Customer } from '@/lib/definitions/customer';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@ourtrip/ui';
import { SealCheck, SealWarning } from '@phosphor-icons/react';

const CustomersEmail = ({ customer }: { customer: Customer }) => {
  return (
    <div className='flex items-center gap-1'>
      <p className='text-gray-500 text-sm'>{customer.email}</p>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            {customer.verified ? <SealCheck /> : <SealWarning />}
          </TooltipTrigger>
          <TooltipContent>
            {customer.verified
              ? 'E-mail verificado!'
              : 'E-mail não verificado...'}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default CustomersEmail;
