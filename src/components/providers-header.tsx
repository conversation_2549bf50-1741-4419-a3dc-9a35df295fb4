'use client';

import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { createProvider } from '@/services/provider';
import { Provider } from '@/lib/definitions/provider';
import ProviderSheet from './provider-sheet';
import { toast } from 'sonner';

const ProvidersHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (provider: Provider) => {
    setIsCreating(true);
    const response = await createProvider(provider);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Provedor criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <ProviderSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar provedor
        </Button>
      </ProviderSheet>
    </div>
  );
};

export default ProvidersHeader;
