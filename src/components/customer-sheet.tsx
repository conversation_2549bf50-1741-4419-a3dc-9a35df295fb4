'use client';

import { CustomerRegisterPayload } from '@/lib/definitions/customer';
import {
  Button,
  Input,
  PhoneInput,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Sheet,
  SheetClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@ourtrip/ui';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useHookFormMask } from 'use-mask-input';

const CustomerSheet = ({
  children,
  user,
  submit,
  loading
}: {
  children: React.ReactNode;
  user?: CustomerRegisterPayload;
  submit: (data: CustomerRegisterPayload) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    setFocus,
    watch,
    formState: { errors }
  } = useForm<CustomerRegisterPayload>({
    defaultValues: {
      documentType: user?.documentType || 'cpf',
      lang: 'PT_BR'
    }
  });

  const documentType = watch('documentType');
  const registerWithMask = useHookFormMask(register);

  const handleSearchPostalCode = async (postalCode: string) => {
    if (postalCode.length === 8) {
      const response = await fetch(
        `https://viacep.com.br/ws/${postalCode}/json/`
      );
      const data = await response.json();
      if (data) {
        setValue('address.street', data.logradouro);
        setValue('address.neighborhood', data.bairro);
        setValue('address.city', data.localidade);
        setValue('address.uf', data.uf);
        setValue('address.country', 'Brasil');
        setValue('address.postalCode', data.cep);
        setValue('address.complement', data.complemento);
        setFocus('address.number');
      }
    }
  };

  const onSubmit = async (data: CustomerRegisterPayload) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>Adicionar Cliente</SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-4 mt-2'>
            <div className='flex gap-2'>
              <Input
                placeholder='Nome *'
                {...register('name', { required: true })}
                error={errors.name?.message}
                color='gray'
              />
              <Input
                placeholder='Sobrenome *'
                {...register('surname', { required: true })}
                error={errors.surname?.message}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Select
                {...register('documentType')}
                onValueChange={value => setValue('documentType', value)}
                defaultValue={user?.documentType || 'cpf'}
                required
              >
                <SelectTrigger className='w-min'>
                  <SelectValue placeholder='Tipo' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='cpf'>CPF</SelectItem>
                  <SelectItem value='cnpj'>CNPJ</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder='Número do documento *'
                {...registerWithMask('documentNumber', documentType, {
                  required: true
                })}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Select {...register('gender')}>
                <SelectTrigger className='w-min'>
                  <SelectValue placeholder='Gênero' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='individual'>Masculino</SelectItem>
                  <SelectItem value='corporate'>Feminino</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type='date'
                placeholder='Data de nascimento'
                {...register('birthday')}
                error={errors.birthday?.message}
                color='gray'
              />
            </div>
            <PhoneInput
              DDI='+55'
              phoneDDIKey={'phone.ddi'}
              ddiLabel='DDI *'
              phoneAreaCodeKey={'phone.areaCode'}
              areaCodeLabel='DDD *'
              invalidAreaCodeMessage='DDD inválido'
              numberLabel='Número *'
              requiredMessage='Campo obrigatório'
              invalidPhoneNumberMessage='Número inválido'
              phoneNumberKey={'phone.number'}
              registerWithMask={registerWithMask}
              setFocus={setFocus}
              errors={errors}
            />
            <p className='text-primary-900'>Endereço</p>
            <div className='flex gap-2'>
              <Input
                placeholder='CEP'
                className='flex-1/3'
                {...register('address.postalCode', {
                  onBlur: e => handleSearchPostalCode(e.target.value)
                })}
                color='gray'
              />
              <Input
                placeholder='Rua'
                className='flex-2/3'
                {...register('address.street')}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Input
                className='flex-1/3'
                placeholder='Número'
                {...register('address.number')}
                color='gray'
              />
              <Input
                className='flex-2/3'
                placeholder='Complemento'
                {...register('address.complement')}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Input
                placeholder='Bairro'
                {...register('address.neighborhood')}
                color='gray'
              />
              <Input
                placeholder='Cidade'
                {...register('address.city')}
                color='gray'
              />
            </div>
            <div className='flex gap-2'>
              <Input
                placeholder='Estado'
                {...register('address.uf')}
                color='gray'
              />
              <Input
                placeholder='País'
                {...register('address.country')}
                color='gray'
              />
            </div>
            <p className='text-primary-900'>Conta</p>
            <Input
              placeholder='E-mail do cliente *'
              {...register('email', { required: true })}
              error={errors.email?.message}
              color='gray'
            />
            <div className='flex gap-2'>
              <Input
                placeholder='Senha *'
                type='password'
                {...register('password', { required: true })}
                error={errors.password?.message}
                color='gray'
              />
              <Input
                type='password'
                placeholder='Senha novamente *'
                {...register('passwordAgain', { required: true })}
                error={errors.passwordAgain?.message}
                color='gray'
              />
            </div>
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {user ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default CustomerSheet;
