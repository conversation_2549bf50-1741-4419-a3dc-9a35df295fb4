'use client';

import { Order } from '@/lib/definitions/order';
import { Button, Modal } from '@ourtrip/ui';
import { X, XCircle } from '@phosphor-icons/react';
import { useForm } from 'react-hook-form';

const OrderCancelModal = ({
  order,
  open,
  onClose,
  onConfirm,
  isLoading = false
}: {
  order: Order;
  open: boolean;
  onClose: () => void;
  onConfirm: (message: string) => void;
  isLoading?: boolean;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<{
    amount: number;
    message: string;
  }>({
    defaultValues: { message: '' }
  });

  const submit = (values: { message: string }) => {
    onConfirm(values.message);
  };

  return (
    <Modal isOpen={open} onClose={onClose}>
      <form className='flex flex-col gap-3 p-4' onSubmit={handleSubmit(submit)}>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <XCircle className='text-primary-900' />
            <p className='text-gray-500'>Confirmar cancelamento</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col px-1 w-[400px]'>
          <p className='text-primary-900 font-medium '>
            Você tem certeza que deseja solicitar o cancelamento deste pedido?
          </p>
          <span className='text-sm text-gray-500 font-normal mt-2'>
            {order?.cancellationInfo?.infos?.map(info => (
              <>
                <p key={info}>{info}</p>
              </>
            ))}
          </span>
          <textarea
            className={`w-full text-sm mt-3 p-2 border border-gray-200 rounded-inner resize-none ${errors.message ? 'border-red-400' : ''}`}
            placeholder='Motivo do cancelamento*'
            {...register('message', { required: true })}
          />
        </div>
        <div className='flex justify-end gap-2'>
          <Button
            className='w-min'
            type='button'
            onClick={onClose}
            color='white'
          >
            Não
          </Button>
          <Button
            type='submit'
            className='w-min'
            color='danger'
            loading={isLoading}
          >
            Sim, cancelar
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default OrderCancelModal;
