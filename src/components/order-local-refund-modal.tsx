'use client';

import { Order } from '@/lib/definitions/order';
import { Button, Input, Modal } from '@ourtrip/ui';
import { ArrowArcLeft, X } from '@phosphor-icons/react';
import { useForm } from 'react-hook-form';

const OrderLocalRefundModal = ({
  open,
  onClose,
  onConfirm,
  isLoading = false
}: {
  order: Order;
  open: boolean;
  onClose: () => void;
  onConfirm: (message: string) => void;
  isLoading?: boolean;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<{
    message: string;
  }>({
    defaultValues: { message: '' }
  });

  const submit = async (values: { message: string }) => {
    onConfirm(values.message);
  };

  return (
    <Modal isOpen={open} onClose={onClose}>
      <form className='flex flex-col gap-3 p-4' onSubmit={handleSubmit(submit)}>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <ArrowArcLeft className='text-primary-900' />
            <p className='text-gray-500'>Marcar como reembolsado</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col px-1 w-[400px]'>
          <p className='text-primary-900 font-medium'>
            Você tem certeza que deseja marcar este pedido como reembolsado?
          </p>
          <textarea
            className={`w-full text-sm mt-3 p-2 border border-gray-200 rounded-inner resize-none ${errors.message ? 'border-red-400' : ''}`}
            placeholder='Motivo do reembolso*'
            {...register('message', { required: true })}
          />
        </div>
        <div className='flex justify-end gap-2'>
          <Button className='w-min' onClick={onClose} color='white'>
            Não
          </Button>
          <Button
            type='submit'
            className='w-min'
            color='danger'
            loading={isLoading}
          >
            Sim, marcar como reembolsado
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default OrderLocalRefundModal;
