'use client';

import { <PERSON><PERSON>, <PERSON>over, <PERSON>overContent, PopoverTrigger } from '@ourtrip/ui';
import { DotsThree, Pencil } from '@phosphor-icons/react';
import { useState } from 'react';
import { AdminProfile } from '@/lib/definitions/admin';
import ProfileSheet from './profile-sheet';
import { updateProfile } from '@/services/admin';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const ProfileActions = ({ profile }: { profile?: AdminProfile }) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const handleUpdateProfile = async (profile: AdminProfile) => {
    setIsEditing(true);
    const response = await updateProfile(profile);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Perfil atualizado com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };
  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <ProfileSheet
            profile={profile}
            submit={handleUpdateProfile}
            loading={isEditing}
          >
            <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
              <Pencil />
              <p className='text-sm text-primary-900 font-medium'>Editar</p>
            </div>
          </ProfileSheet>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ProfileActions;
