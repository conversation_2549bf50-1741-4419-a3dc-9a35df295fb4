'use client';

import { Order } from '@/lib/definitions/order';
import { Divider } from '@ourtrip/ui';

const OrderCustomer = ({ order }: { order: Order }) => {
  return (
    <div className='flex flex-col gap-3 bg-white p-4 rounded-default'>
      <div className='flex flex-col'>
        <h3 className='text-xs text-gray-500'>Cliente</h3>
        <p className=' text-primary-900 font-medium text-sm'>
          {order.customer?.name || ''} {order.customer?.surname || ''}
        </p>
      </div>
      <div className='flex gap-4'>
        <div className='flex flex-col'>
          <h3 className='text-xs text-gray-500'>Telefone</h3>
          <p className=' text-primary-900 font-medium text-sm'>
            {order.customer?.phone.ddi} {order.customer?.phone.areaCode}{' '}
            {order.customer?.phone.number || '-'}
          </p>
        </div>
        <div className='flex flex-col'>
          <h3 className='text-xs text-gray-500'>
            {order.customer.documentType === 'CPF' ? 'CPF' : 'CNPJ'}
          </h3>
          <p className=' text-primary-900 font-medium text-sm'>
            {order.customer.documentNumber || '-'}
          </p>
        </div>
      </div>
      <div className='flex flex-col'>
        <h3 className='text-xs text-gray-500'>Email</h3>
        <p className=' text-primary-900 font-medium text-sm'>
          {order.customer?.email || '-'}
        </p>
      </div>
      <Divider orientation='horizontal' />
      <div className='flex flex-col'>
        <h3 className='text-xs text-gray-500'>Endereço</h3>
        <p className=' text-primary-900 font-medium text-sm'>
          {order.customer?.address?.street || '-'},{' '}
          {order.customer?.address?.number || '-'}
        </p>
      </div>
      <div className='flex flex-col'>
        <h3 className='text-xs text-gray-500'>Bairro</h3>
        <p className=' text-primary-900 font-medium text-sm'>
          {order.customer?.address?.neighborhood || '-'}
        </p>
      </div>
      <div className='flex gap-4'>
        <div className='flex flex-col'>
          <h3 className='text-xs text-gray-500'>Cidade/Estado</h3>
          <p className=' text-primary-900 font-medium text-sm'>
            {order.customer?.address?.city || '-'}/
            {order.customer?.address?.uf || '-'}
          </p>
        </div>
        <div className='flex flex-col'>
          <h3 className='text-xs text-gray-500'>CEP</h3>
          <p className=' text-primary-900 font-medium text-sm'>
            {order.customer?.address?.postalCode || '-'}
          </p>
        </div>
      </div>
      <div className='flex flex-col'>
        <h3 className='text-xs text-gray-500'>Complement</h3>
        <p className=' text-primary-900 font-medium text-sm'>
          {order.customer?.address?.complement || '-'}
        </p>
      </div>
    </div>
  );
};

export default OrderCustomer;
