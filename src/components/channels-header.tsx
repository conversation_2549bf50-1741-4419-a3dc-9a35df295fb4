'use client';

import { useState } from 'react';
import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { createChannel } from '@/services/channel';
import { Channel } from '@/lib/definitions/channel';
import ChannelSheet from './channel-sheet';
import { toast } from 'sonner';

const ChannelsHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (channel: Channel) => {
    setIsCreating(true);
    const response = await createChannel(channel);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Canal criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <ChannelSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar canal
        </Button>
      </ChannelSheet>
    </div>
  );
};

export default ChannelsHeader;
