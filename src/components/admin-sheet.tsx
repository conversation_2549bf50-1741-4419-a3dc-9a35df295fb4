'use client';

import { Admin, AdminProfile } from '@/lib/definitions/admin';
import { getProfiles } from '@/services/admin';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Sheet,
  SheetClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@ourtrip/ui';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const AdminSheet = ({
  children,
  admin,
  submit,
  loading
}: {
  children: React.ReactNode;
  admin?: Admin;
  submit: (data: Admin & { confirmPassword?: string }) => Promise<void>;
  loading: boolean;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [profiles, setProfiles] = useState<AdminProfile[]>([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors }
  } = useForm<Admin & { confirmPassword?: string }>({
    defaultValues: {
      id: admin?.id || '',
      name: admin?.name || '',
      email: admin?.email || '',
      enabled: admin?.enabled || true,
      profiles: admin?.profiles || []
    }
  });

  const fetchProfiles = async () => {
    const response = await getProfiles();

    if (response.error) {
      toast.error(response.error);
    } else {
      setProfiles(response.data || []);
    }
  };

  const onSubmit = async (data: Admin) => {
    try {
      await submit(data);
      setIsOpen(false);
      reset();
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  useEffect(() => {
    fetchProfiles();
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='overflow-auto scrollbar'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <SheetHeader>
            <SheetTitle>
              {admin ? 'Editar administrador' : 'Criar administrador'}
            </SheetTitle>
          </SheetHeader>
          <div className='flex flex-col gap-2 mt-2'>
            <Input
              {...register('name', { required: 'Campo obrigatório' })}
              error={errors?.name?.message}
              placeholder='Nome do administrador'
              color='gray'
            />
            <Input
              {...register('email', { required: 'Campo obrigatório' })}
              error={errors?.email?.message}
              placeholder='E-mail do administrador'
              color='gray'
            />
            <Select
              onValueChange={value => {
                setValue('enabled', value === 'true');
              }}
              defaultValue={
                admin ? (admin?.enabled ? 'true' : 'false') : 'true'
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='Ativo ou não' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={'true'}>Ativo</SelectItem>
                <SelectItem value={'false'}>Inativo</SelectItem>
              </SelectContent>
            </Select>
            <Select
              {...register('profiles')}
              onValueChange={value => {
                setValue('profiles', [
                  profiles.find(profile => profile.id === value)!
                ]);
              }}
              defaultValue={admin?.profiles?.[0]?.id || ''}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder='Selecionar perfil' />
              </SelectTrigger>
              <SelectContent>
                {profiles.map(profile => (
                  <SelectItem key={profile.id} value={profile.id!}>
                    {profile.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!admin && (
              <div className='flex gap-2'>
                <Input
                  type='password'
                  {...register('password', { required: 'Campo obrigatório' })}
                  error={errors?.password?.message}
                  placeholder='Senha'
                  color='gray'
                />
                <Input
                  type='password'
                  {...register('confirmPassword', {
                    required: 'Campo obrigatório'
                  })}
                  error={errors?.confirmPassword?.message}
                  placeholder='Senha novamente'
                  color='gray'
                />
              </div>
            )}
          </div>
          <SheetFooter className='flex items-center justify-between mt-4'>
            <SheetClose asChild>
              <Button
                type='button'
                variant='outline'
                onClick={() => {
                  setIsOpen(false);
                  reset();
                }}
              >
                Cancelar
              </Button>
            </SheetClose>
            <Button
              type='submit'
              color='primary'
              className='w-full'
              loading={loading}
            >
              {admin ? 'Salvar' : 'Criar'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default AdminSheet;
