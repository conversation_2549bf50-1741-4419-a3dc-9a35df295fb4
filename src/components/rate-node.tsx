import React, { memo } from 'react';
import { ShareNetwork } from '@phosphor-icons/react';
import { Handle, Position } from '@xyflow/react';
import { Divider } from '@ourtrip/ui';

const RateNode = memo(({ data }: { data: any }) => {
  return (
    <>
      <div className='flex flex-col bg-white rounded-default px-2 py-2 gap-2'>
        <Handle type='source' position={Position.Right} />
        <Handle
          id={data.handleChannelId}
          type='source'
          position={Position.Left}
        />
        <div className='flex items-center justify-between gap-6'>
          <div className='flex items-center gap-2'>
            <ShareNetwork />
            <p className='text-gray-500 text-sm'>Taxa</p>
          </div>
        </div>
        <div className='flex gap-4 items-center px-2'>
          <div className='w-12 flex flex-col justify-center'>
            <p className='text-gray-500 text-sm text-center'>Markup</p>
            <p className='text-primary-900 font-medium text-center'>
              {data.markup}
            </p>
          </div>
          <Divider />
          <div className='w-12 flex flex-col justify-center'>
            <p className='text-gray-500 text-sm text-center'>Taxa</p>
            <p className='text-primary-900 font-medium text-center'>
              {data.tax}
            </p>
          </div>
        </div>
      </div>
    </>
  );
});

export default RateNode;
