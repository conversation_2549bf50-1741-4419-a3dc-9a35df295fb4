'use client';

import { Button, Modal } from '@ourtrip/ui';
import { Upload, X } from '@phosphor-icons/react';

const SendNFeModal = ({
  open,
  onClose,
  onConfirm,
  isLoading = false,
  competence
}: {
  competence: string;
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}) => {
  return (
    <Modal isOpen={open} onClose={onClose}>
      <div className='flex flex-col gap-3 p-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Upload className='text-primary-900' />
            <p className='text-gray-500'>Enviar NF-e</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col px-1 w-[400px]'>
          <p className='text-primary-900 '>
            Você tem certeza que deseja enviar esse arquivo para a competência{' '}
            {competence}?
          </p>
        </div>
        <div className='flex justify-end gap-2'>
          <Button className='w-min' onClick={onClose} color='white'>
            Não
          </Button>
          <Button
            className='w-min'
            onClick={onConfirm}
            color='primary'
            loading={isLoading}
          >
            Sim, enviar
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SendNFeModal;
