'use client';

import { Order } from '@/lib/definitions/order';
import {
  ArrowArcLeft,
  CreditCard,
  Download,
  Prohibit,
  Receipt,
  X
} from '@phosphor-icons/react';
import SidebarItem from './sidebar-item';
import SelectResponsableModal from './select-responsable-modal';
import { useState } from 'react';
import {
  cancelOrder,
  localCancelOrder,
  localRefundOrder,
  refundOrder
} from '@/services/order';
import { useRouter } from 'next/navigation';
import OrderCancelModal from './order-cancel-modal';
import OrderLocalCancelModal from './order-local-cancel-modal';
import OrderRefundModal from './order-refund-modal';
import OrderLocalRefundModal from './order-local-refund-modal';
import { toast } from 'sonner';
import { Divider } from '@ourtrip/ui';

const OrderActions = ({ order }: { order: Order }) => {
  const router = useRouter();
  const [orderCancelModalOpen, setOrderCancelModalOpen] =
    useState<boolean>(false);
  const [orderLocalCancelModalOpen, setOrderLocalCancelModalOpen] =
    useState<boolean>(false);
  const [orderRefundModalOpen, setOrderRefundModalOpen] =
    useState<boolean>(false);
  const [orderLocalRefundModalOpen, setOrderLocalRefundModalOpen] =
    useState<boolean>(false);
  const [selectResponsableModalOpen, setSelectResponsableModalOpen] =
    useState<boolean>(false);
  const [isCanceling, setIsCanceling] = useState<boolean>(false);
  const [isRefunding, setIsRefunding] = useState<boolean>(false);

  const handleCancelOrder = async (message: string) => {
    setIsCanceling(true);

    const response = await cancelOrder({
      orderId: order.id,
      message
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Pedido cancelado com sucesso!');
      router.refresh();
    }

    setOrderCancelModalOpen(false);
    setIsCanceling(false);
  };

  const handleLocalCancelOrder = async (message: string) => {
    setIsCanceling(true);

    const response = await localCancelOrder({
      orderId: order.id,
      message
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Pedido cancelado com sucesso!');
      router.refresh();
    }

    setOrderLocalCancelModalOpen(false);
    setIsCanceling(false);
  };

  const handleRefund = async (amount: number, message: string) => {
    setIsRefunding(true);

    const response = await refundOrder({
      orderId: order.id,
      amount,
      message
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Reembolso solicitado com sucesso');
      router.refresh();
    }

    setOrderRefundModalOpen(false);
    setIsRefunding(false);
  };

  const handleLocalRefund = async (message: string) => {
    setIsRefunding(true);

    const response = await localRefundOrder({
      orderId: order.id,
      message
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Reembolso solicitado com sucesso');
      router.refresh();
    }

    setOrderLocalRefundModalOpen(false);
    setIsRefunding(false);
  };

  const handleDownloadVoucher = () => {
    window.open(`/${order.lang}/voucher/${order.id}`, '_blank');
  };

  const handleDownloadReceipt = (responsable: string) => {
    window.open(
      `/${order.lang}/receipt/${order.id}?responsable=${responsable}`,
      '_blank'
    );
  };

  return (
    <div className='rounded-default bg-white flex flex-col p-4'>
      {!order.cancellationDate && (
        <SidebarItem
          label='Cancelar pedido'
          icon={Prohibit}
          onClick={() => setOrderCancelModalOpen(true)}
        />
      )}
      {!order.refundDate && (
        <SidebarItem
          label='Marcar como cancelado'
          icon={X}
          onClick={() => setOrderLocalCancelModalOpen(true)}
        />
      )}
      <Divider orientation='horizontal' className='my-2' />
      {!order.refundDate && (
        <SidebarItem
          label='Solicitar reembolso'
          icon={CreditCard}
          onClick={() => setOrderRefundModalOpen(true)}
        />
      )}
      {!order.refundDate && (
        <SidebarItem
          label='Marcar como reembolsado'
          icon={ArrowArcLeft}
          onClick={() => setOrderLocalRefundModalOpen(true)}
        />
      )}
      <Divider orientation='horizontal' className='my-2' />
      {order.status === 'CONFIRMED' && (
        <SidebarItem
          label='Baixar voucher'
          icon={Download}
          onClick={handleDownloadVoucher}
        />
      )}
      {order.status === 'CONFIRMED' && (
        <SidebarItem
          label='Baixar recibo'
          icon={Receipt}
          onClick={() => setSelectResponsableModalOpen(true)}
        />
      )}
      <OrderCancelModal
        open={orderCancelModalOpen}
        order={order}
        onClose={() => setOrderCancelModalOpen(false)}
        onConfirm={handleCancelOrder}
        isLoading={isCanceling}
      />
      <OrderLocalCancelModal
        open={orderLocalCancelModalOpen}
        order={order}
        onClose={() => setOrderLocalCancelModalOpen(false)}
        onConfirm={handleLocalCancelOrder}
        isLoading={isCanceling}
      />
      <OrderRefundModal
        open={orderRefundModalOpen}
        order={order}
        onClose={() => setOrderRefundModalOpen(false)}
        onConfirm={handleRefund}
        isLoading={isRefunding}
      />
      <OrderLocalRefundModal
        open={orderLocalRefundModalOpen}
        order={order}
        onClose={() => setOrderLocalRefundModalOpen(false)}
        onConfirm={handleLocalRefund}
        isLoading={isRefunding}
      />
      <SelectResponsableModal
        open={selectResponsableModalOpen}
        order={order}
        onClose={() => setSelectResponsableModalOpen(false)}
        onDownload={handleDownloadReceipt}
      />
    </div>
  );
};

export default OrderActions;
