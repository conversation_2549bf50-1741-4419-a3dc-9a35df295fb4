'use client';

import { But<PERSON> } from '@ourtrip/ui';
import {
  ArrowClockwise,
  ArrowLeft,
  CircleNotch,
  Pause,
  Play
} from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { getTrivagoServiceLogs } from '@/services/timi';

const LogViewer = () => {
  const router = useRouter();
  const [logs, setLogs] = useState('');
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(10000);
  const [tailLines, setTailLines] = useState(100);
  const [showTail, setShowTail] = useState(true);
  const logContainerRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchLogs = async () => {
    if (logs.length === 0) setLoading(true);

    try {
      const response = await getTrivagoServiceLogs();

      if (response.data) {
        setLogs(response.data);
      } else {
        toast.error(response.error);
      }
    } catch (err: any) {
      toast.error(`Falha ao buscar logs: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  useEffect(() => {
    if (autoRefresh) {
      intervalRef.current = setInterval(fetchLogs, refreshInterval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, tailLines, showTail]);

  useEffect(() => {
    fetchLogs();
  }, []);

  return (
    <div className='flex flex-col w-full h-full gap-3 overflow-hidden'>
      <div className='flex justify-between gap-3'>
        <Button
          onClick={() => router.back()}
          disabled={loading}
          color='white'
          size='small'
        >
          <ArrowLeft />
          Voltar
        </Button>
        <div className='flex gap-3'>
          <Button
            onClick={fetchLogs}
            disabled={loading}
            color='white'
            size='small'
          >
            {loading ? (
              <CircleNotch className='w-4 h-4 animate-spin' />
            ) : (
              <ArrowClockwise />
            )}
            Atualizar
          </Button>
          <Button
            color='white'
            size='small'
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? (
              <Pause className='w-4 h-4' />
            ) : (
              <Play className='w-4 h-4' />
            )}
            Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
        </div>
      </div>
      <div
        ref={logContainerRef}
        className='bg-white text-gray-700 p-3 rounded-default text-sm overflow-auto scrollbar'
      >
        {loading ? (
          <div className='text-gray-400'>Carregando logs...</div>
        ) : logs ? (
          <pre className='whitespace-pre-wrap'>{logs}</pre>
        ) : (
          <div className='text-gray-400'>Nenhum log disponível</div>
        )}
      </div>
    </div>
  );
};

export default LogViewer;
