'use client';

import { Channel } from '@/lib/definitions/channel';
import { updateChannel } from '@/services/channel';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Pause,
  Pencil,
  Play
} from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import ChannelSheet from './channel-sheet';
import { toast } from 'sonner';

const ChannelsActions = ({ channel }: { channel: Channel }) => {
  const router = useRouter();
  const [isToggling, setIsToggling] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const handleUpdateChannel = async (channel: Channel) => {
    setIsEditing(true);
    const response = await updateChannel(channel);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Canal atualizado com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };

  const handleToggleActive = async () => {
    setIsToggling(true);
    const response = await updateChannel({
      ...channel,
      enabled: !channel.enabled
    });

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Canal atualizado com sucesso');
      router.refresh();
    }

    setIsToggling(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col gap-1'>
            <ChannelSheet
              channel={channel}
              submit={handleUpdateChannel}
              loading={isEditing}
            >
              <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
                <Pencil />
                <p className='text-sm text-primary-900 font-medium'>Editar</p>
              </div>
            </ChannelSheet>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={handleToggleActive}
            >
              {isToggling ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : !channel.enabled ? (
                <Play />
              ) : (
                <Pause />
              )}
              <p className='text-sm text-primary-900 font-medium'>
                {!channel.enabled ? 'Habilitar' : 'Desabilitar'}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ChannelsActions;
