'use client';

import { Provider } from '@/lib/definitions/provider';
import { Badge } from '@ourtrip/ui';
import { Check, X } from '@phosphor-icons/react';

const ProviderStatus = ({ provider }: { provider: Provider }) => {
  return (
    <Badge
      className='flex w-min'
      type={provider.enabled ? 'success' : 'danger'}
      icon={provider.enabled ? <Check /> : <X />}
      size='small'
    >
      {provider.enabled ? 'Ativo' : 'Inativo'}
    </Badge>
  );
};

export default ProviderStatus;
