import React, { memo } from 'react';
import { ShareNetwork } from '@phosphor-icons/react';
import { Badge } from '@ourtrip/ui';
import { Handle, Position } from '@xyflow/react';

const ProviderAccountNode = memo(({ data }: { data: any }) => {
  return (
    <>
      <div
        className={`flex flex-col bg-white rounded-default px-2 py-2 gap-2 ${data.enabled ? 'opacity-100' : 'opacity-70'}`}
      >
        <Handle id={data.id} type='source' position={Position.Right} />
        <Handle id={data.code} type='target' position={Position.Left} />
        <div className='flex items-center justify-between gap-6'>
          <div className='flex items-center gap-2'>
            <ShareNetwork />
            <p className='text-gray-500 text-sm'>Conta de Provedor</p>
          </div>
          <Badge size='small' type={data.enabled ? 'success' : 'danger'}>
            {data.enabled ? 'Ativado' : 'Desativado'}
          </Badge>
        </div>
        <div className='flex gap-4 items-center px-2'>
          <div className='flex flex-col'>
            <p className='text-gray-500 text-sm'>Código</p>
            <p className='text-primary-900 font-medium'>{data.code}</p>
          </div>
        </div>
      </div>
    </>
  );
});

export default ProviderAccountNode;
