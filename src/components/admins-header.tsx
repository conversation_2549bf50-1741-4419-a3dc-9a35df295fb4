'use client';

import { Admin } from '@/lib/definitions/admin';
import { createAdmin } from '@/services/admin';
import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import AdminSheet from './admin-sheet';
import { useState } from 'react';
import { toast } from 'sonner';

const AdminsHeader = () => {
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const router = useRouter();

  const handleCreate = async (admin: Admin & { confirmPassword?: string }) => {
    setIsCreating(true);
    if (admin.password !== admin.confirmPassword) return;

    const response = await createAdmin(admin);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Administrador criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <AdminSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar administrador
        </Button>
      </AdminSheet>
    </div>
  );
};

export default AdminsHeader;
