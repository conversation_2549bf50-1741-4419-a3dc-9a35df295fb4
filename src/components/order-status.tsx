'use client';

import { orderStatus, StatusType } from '@/lib/utils/status';
import { Badge } from '@ourtrip/ui';

const OrderStatus = ({ status }: { status: StatusType }) => {
  return (
    <Badge
      className='flex w-min'
      type={orderStatus[status]?.type as any}
      icon={orderStatus[status]?.icon}
      size='small'
    >
      {orderStatus[status]?.text}
    </Badge>
  );
};

export default OrderStatus;
