import React, { memo } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { ShareNetwork } from '@phosphor-icons/react';
import { Badge, Divider } from '@ourtrip/ui';
import { BaseHandle } from './base-handle';

const ProviderNode = memo(
  ({ data, isConnectable }: { data: any; isConnectable: boolean }) => {
    return (
      <>
        <Handle
          id={data.id}
          type='target'
          position={Position.Left}
          isConnectable={isConnectable}
        />
        <div className='flex flex-col bg-white rounded-default px-2 py-2 gap-2'>
          <div className='flex items-center justify-between gap-6'>
            <div className='flex items-center gap-2'>
              <ShareNetwork />
              <p className='text-gray-500 text-sm'>Provedor</p>
            </div>
            <Badge size='small' type={data.enabled ? 'success' : 'danger'}>
              {data.enabled ? 'Ativado' : 'Desativado'}
            </Badge>
          </div>
          <div className='flex gap-4 items-center px-2'>
            <div className='flex flex-col'>
              <p className='text-gray-500 text-sm'>Nome</p>
              <p className='text-primary-900 font-medium'>{data.label}</p>
            </div>
            <Divider />
            <div className='flex flex-col'>
              <p className='text-gray-500 text-sm'>Código</p>
              <p className='text-primary-900 font-medium'>{data.code}</p>
            </div>
          </div>
        </div>
      </>
    );
  }
);

export default ProviderNode;
