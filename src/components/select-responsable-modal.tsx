'use client';

import { Order } from '@/lib/definitions/order';
import { Button, Modal } from '@ourtrip/ui';
import { Check, Receipt, X } from '@phosphor-icons/react';
import { useState } from 'react';

const SelectResponsableModal = ({
  open,
  order,
  onClose,
  onDownload
}: {
  order: Order;
  open: boolean;
  onClose: () => void;
  onDownload: (responsable: 'payer' | 'guest') => void;
}) => {
  const [responsable, setResponsable] = useState<'payer' | 'guest'>('payer');

  const handleDownload = (responsable: 'payer' | 'guest') => {
    onDownload(responsable);
    onClose();
  };

  const handleSelect = (type: 'payer' | 'guest') => {
    setResponsable(type);
  };

  return (
    <Modal isOpen={open} onClose={onClose}>
      <div className='flex flex-col gap-4 p-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Receipt className='text-primary-900' />
            <p className='text-gray-500'>Baixar recibo</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col gap-2'>
          <div
            onClick={() => handleSelect('payer')}
            className='flex justify-between items-center px-4 py-3 bg-gray-100 rounded-inner cursor-pointer'
          >
            <p className='font-medium text-primary-900'>
              Responsável financeiro
            </p>
            {responsable === 'payer' && <Check className='text-primary-900' />}
          </div>
          <div
            onClick={() => handleSelect('guest')}
            className='flex justify-between items-center px-4 py-3 bg-gray-100 rounded-inner cursor-pointer'
          >
            <p className='font-medium text-primary-900'>Hóspede</p>
            {responsable === 'guest' && <Check className='text-primary-900' />}
          </div>
        </div>
        <Button onClick={() => handleDownload(responsable!)} color='primary'>
          Baixar
        </Button>
      </div>
    </Modal>
  );
};

export default SelectResponsableModal;
