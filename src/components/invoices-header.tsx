'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  processPendingInvoices,
  resendErrorsInvoices
} from '@/services/invoice';
import { Button } from '@ourtrip/ui';
import {
  ArrowsCounterClockwise,
  CircleNotch,
  Play
} from '@phosphor-icons/react';
import { InvoiceResponse } from '@/lib/definitions/invoice';
import { toast } from 'sonner';

const InvoicesHeader = ({ errors }: { errors: InvoiceResponse }) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isResending, setIsResending] = useState<boolean>(false);

  const handleProcessPending = async () => {
    setIsProcessing(true);
    const response = await processPendingInvoices();

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais processadas com sucesso');
      router.refresh();
    }

    setIsProcessing(false);
  };

  const handleResendErrors = async () => {
    setIsResending(true);

    const response = await resendErrorsInvoices(
      errors.content.map(invoice => invoice.idNfe)
    );

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais reenviadas com sucesso');
      router.refresh();
    }

    setIsResending(false);
  };

  return (
    <div className='flex justify-between gap-2'>
      <Button
        color='white'
        size='small'
        className='w-min'
        onClick={handleProcessPending}
      >
        {isProcessing ? (
          <CircleNotch className='animate-spin' weight='bold' />
        ) : (
          <Play />
        )}
        Processar Pendentes
      </Button>
      <Button
        color='white'
        size='small'
        className='w-min'
        onClick={handleResendErrors}
      >
        {isResending ? (
          <CircleNotch className='animate-spin' weight='bold' />
        ) : (
          <ArrowsCounterClockwise />
        )}
        Reenviar Erros
      </Button>
    </div>
  );
};

export default InvoicesHeader;
