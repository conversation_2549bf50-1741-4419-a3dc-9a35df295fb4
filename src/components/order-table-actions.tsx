'use client';

import { Order } from '@/lib/definitions/order';
import { <PERSON>ton, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import { DotsThree, <PERSON> } from '@phosphor-icons/react';
import Link from 'next/link';

const OrderActions = ({ order }: { order: Order }) => {
  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <Link
            href={`/orders/${order.id}`}
            className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
          >
            <Eye />
            <p className='text-sm text-primary-900 font-medium'>Ver detalhes</p>
          </Link>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default OrderActions;
