'use client';

import { Badge } from '@ourtrip/ui';
import { Check, X } from '@phosphor-icons/react';

const TripcashStatus = ({ active }: { active: boolean }) => {
  return active ? (
    <Badge type='success' size='small' icon={<Check />} className='flex w-min'>
      Ativo
    </Badge>
  ) : (
    <Badge type='danger' size='small' icon={<X />} className='flex w-min'>
      Desativado
    </Badge>
  );
};

export default TripcashStatus;
