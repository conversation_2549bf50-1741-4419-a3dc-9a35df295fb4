'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import { CircleNotch, DotsThree, <PERSON>cil, Trash } from '@phosphor-icons/react';
import RateSheet from './rate-sheet';
import { Rate } from '@/lib/definitions/rate';
import { deleteRate, updateRate } from '@/services/rate';
import { toast } from 'sonner';

const RateActions = ({ rate }: { rate: Rate }) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    const response = await deleteRate(rate.id!);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Taxa deletada com sucesso');
      router.refresh();
    }

    setIsDeleting(false);
  };

  const handleUpdateRate = async (rate: Rate) => {
    setIsEditing(true);
    const response = await updateRate(rate);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Taxa atualizada com sucesso');
      router.refresh();
    }

    setIsEditing(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col gap-1'>
            <RateSheet
              rate={rate}
              submit={handleUpdateRate}
              loading={isEditing}
            >
              <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
                <Pencil />
                <p className='text-sm text-primary-900 font-medium'>Editar</p>
              </div>
            </RateSheet>
            <div
              onClick={handleDelete}
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
            >
              {isDeleting ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : (
                <Trash />
              )}
              <p className='text-sm text-primary-900 font-medium'>Deletar</p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default RateActions;
