'use client';

import { useState } from 'react';
import { Button } from '@ourtrip/ui';
import { Plus } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { AdminProfile } from '@/lib/definitions/admin';
import { createProfile } from '@/services/admin';
import ProfileSheet from './profile-sheet';
import { toast } from 'sonner';

const ProfileHeader = () => {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleCreate = async (profile: AdminProfile) => {
    setIsCreating(true);
    const response = await createProfile(profile);

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Perfil criado com sucesso');
      router.refresh();
    }

    setIsCreating(false);
  };

  return (
    <div className='flex items-center justify-between'>
      <ProfileSheet submit={handleCreate} loading={isCreating}>
        <Button color='white' size='small'>
          <Plus />
          Criar perfil
        </Button>
      </ProfileSheet>
    </div>
  );
};

export default ProfileHeader;
