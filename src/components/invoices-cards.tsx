'use client';

import { useState } from 'react';
import { Button } from '@ourtrip/ui';
import {
  ArrowCounterClockwise,
  ArrowsClockwise,
  CircleNotch,
  Play
} from '@phosphor-icons/react';
import { toast } from 'sonner';
import {
  processPendingInvoices,
  resendErrorsInvoices,
  updatePendingInvoices
} from '@/services/invoice';
import { useRouter } from 'next/navigation';
import { InvoiceResponse } from '@/lib/definitions/invoice';

const InvoicesCards = ({
  invoiceSubmissionStatus,
  errors
}: {
  invoiceSubmissionStatus: any;
  errors: InvoiceResponse;
}) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [isResending, setIsResending] = useState<boolean>(false);

  const handleProcessPending = async () => {
    setIsProcessing(true);
    const response = await processPendingInvoices();

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais processadas com sucesso');
      router.refresh();
    }

    setIsProcessing(false);
  };

  const handleUpdatePending = async () => {
    setIsUpdating(true);
    const response = await updatePendingInvoices();

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais atualizadas com sucesso');
      router.refresh();
    }

    setIsUpdating(false);
  };

  const handleResendErrors = async () => {
    setIsResending(true);

    const response = await resendErrorsInvoices(
      errors.content.map(invoice => invoice.idNfe)
    );

    if (response.error) {
      toast.error(response.error);
    } else {
      toast.success('Notas fiscais reenviadas com sucesso');
      router.refresh();
    }

    setIsResending(false);
  };

  return (
    <div className='w-full flex gap-3'>
      <div className='w-full flex flex-col items-center gap-2 bg-white rounded-default p-3'>
        <h3 className='text-3xl font-semibold mt-3'>
          {invoiceSubmissionStatus.pendente}
        </h3>
        <p className='text-sm text-gray-500'>Pendentes</p>
        <Button
          color='gray'
          size='small'
          className='w-full mt-2'
          onClick={handleProcessPending}
        >
          {isProcessing ? (
            <CircleNotch className='animate-spin' weight='bold' />
          ) : (
            <Play />
          )}
          Processar
        </Button>
      </div>
      <div className='w-full flex flex-col items-center gap-2 bg-white rounded-default p-3'>
        <h3 className='text-3xl font-semibold mt-3'>
          {invoiceSubmissionStatus.processandoAutorizacao}
        </h3>
        <p className='text-sm text-gray-500'>Processando</p>
        <Button
          color='gray'
          size='small'
          className='w-full mt-2'
          onClick={handleUpdatePending}
        >
          {isUpdating ? (
            <CircleNotch className='animate-spin' weight='bold' />
          ) : (
            <ArrowsClockwise />
          )}
          Atualizar
        </Button>
      </div>
      <div className='w-full flex flex-col items-center gap-2 bg-white rounded-default p-3'>
        <h3 className='text-3xl font-semibold mt-3'>
          {invoiceSubmissionStatus.erro}
        </h3>
        <p className='text-sm text-gray-500'>Erros</p>
        <Button
          color='gray'
          size='small'
          className='w-full mt-2'
          onClick={handleResendErrors}
        >
          {isResending ? (
            <CircleNotch className='animate-spin' weight='bold' />
          ) : (
            <ArrowCounterClockwise />
          )}
          Reenviar
        </Button>
      </div>
    </div>
  );
};

export default InvoicesCards;
