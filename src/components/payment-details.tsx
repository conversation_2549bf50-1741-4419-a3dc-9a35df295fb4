'use client';

import PaymentCard from '@/components/payment-card';
import { CreditCard, HandCoins, StripeLogo } from '@phosphor-icons/react';
import Image from 'next/image';
import { OrderPayment } from '@/lib/definitions/order';
import {
  convertNumberToCurrency,
  PaymentMethodIdEnum
} from '@/lib/utils/currency';
import { PixLogo } from '@phosphor-icons/react/dist/ssr';

const PaymentDetails = ({
  method,
  currency,
  currencySymbol,
  paymentMethodId,
  installmentAmount,
  installments,
  usedTripcash
}: OrderPayment) => {
  const paymentMethod = {
    PIX: <PaymentCard icon={<PixLogo size={18} />} title='Pix' />,
    CREDIT_CARD: (
      <PaymentCard
        icon={
          PaymentMethodIdEnum[paymentMethodId] ? (
            <Image
              width={30}
              height={30}
              src={`/${paymentMethodId}.png`}
              alt={`Ícone da forma de pagamento ${paymentMethodId}`}
            />
          ) : (
            <CreditCard size={18} />
          )
        }
        title='Cartão de Crédito'
        installments={
          installments
            ? `${installments}x de ${currencySymbol} ${convertNumberToCurrency(currency!, installmentAmount!)}`
            : null
        }
      />
    ),
    EXTERNAL: <PaymentCard icon={<StripeLogo size={18} />} title='Externo' />,
    TRIPCASH: (
      <PaymentCard
        icon={<HandCoins size={22} />}
        title='Tripcash'
        description={
          usedTripcash
            ? `Utilizado: ${currencySymbol} ${usedTripcash?.formattedValue.replace('-', '')}`
            : undefined
        }
      />
    )
  };

  return paymentMethod[method];
};

export default PaymentDetails;
