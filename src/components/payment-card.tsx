'use client';

import { ReactNode } from 'react';
import { Badge } from '@ourtrip/ui';
import { ClockCountdown } from '@phosphor-icons/react';

interface IPaymentCard {
  icon?: ReactNode;
  title: string;
  description?: string;
  installments?: string | null;
  expired?: boolean;
}

const PaymentCard = ({
  icon,
  title,
  description,
  installments,
  expired
}: IPaymentCard) => {
  return (
    <div className='flex items-center gap-2'>
      {icon ? <div>{icon}</div> : null}
      <div className='flex flex-col'>
        <h3 className='flex gap-2 text-sm font-medium text-primary-900'>
          {title}
          {expired && (
            <Badge
              size='small'
              type='danger'
              icon={<ClockCountdown size={14} />}
            >
              Pagamento expirado!
            </Badge>
          )}
        </h3>
        {description && <p className='text-gray-500 text-sm'>{description}</p>}
        {installments && (
          <p className='text-gray-500 text-sm'>{installments}</p>
        )}
      </div>
    </div>
  );
};

export default PaymentCard;
