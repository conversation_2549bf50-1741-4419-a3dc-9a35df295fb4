'use client';

import { ProviderAccount } from '@/lib/definitions/provider-account';
import { Badge } from '@ourtrip/ui';
import { Check, X } from '@phosphor-icons/react';

const ProviderAccountStatus = ({
  providerAccount
}: {
  providerAccount: ProviderAccount;
}) => {
  return (
    <Badge
      className='flex w-min'
      type={providerAccount.enabled ? 'success' : 'danger'}
      icon={providerAccount.enabled ? <Check /> : <X />}
      size='small'
    >
      {providerAccount.enabled ? 'Ativo' : 'Inativo'}
    </Badge>
  );
};

export default ProviderAccountStatus;
